# md2word.com 快速部署指南

## 🎯 部署环境

- **域名**：md2word.com, www.md2word.com
- **服务器**：Ubuntu（已有Django网站运行）
- **部署目录**：/srv/md2word
- **应用端口**：8003（避免与现有网站冲突）
- **SSL证书**：Let's Encrypt

## 🚀 一键部署（推荐）

### 方法1：从已克隆项目开始部署（当前状态）

**前提条件**：项目已克隆到 `/srv/md2word`

```bash
# 1. 连接到服务器（已完成）
ssh root@your-server-ip

# 2. 确认项目已克隆（已完成）
ls /srv/md2word  # 应该看到项目文件

# 3. 下载简化部署脚本
cd /srv/md2word
wget https://gitee.com/bin1874/md2word/raw/master/scripts/deploy-from-clone.sh

# 4. 设置执行权限并运行
chmod +x deploy-from-clone.sh
./deploy-from-clone.sh
```

### 方法2：完整部署脚本（从零开始）

```bash
# 1. 连接到服务器
ssh root@your-server-ip

# 2. 下载完整部署脚本
wget https://gitee.com/bin1874/md2word/raw/master/scripts/deploy.sh

# 3. 设置执行权限
chmod +x deploy.sh

# 4. 运行部署脚本
./deploy.sh
```

### 方法3：手动部署（从已克隆项目开始）

**当前状态**：项目已在 `/srv/md2word`

```bash
# 1. 系统准备
apt update && apt upgrade -y
apt install -y python3 python3-pip python3-venv nginx certbot python3-certbot-nginx pandoc

# 2. 配置项目（项目已克隆，跳过克隆步骤）
cd /srv/md2word
git pull origin master  # 更新到最新版本
chown -R www-data:www-data /srv/md2word

# 3. 安装依赖
cd /srv/md2word
sudo -u www-data python3 -m venv venv
sudo -u www-data bash -c "source venv/bin/activate && pip install -r requirements.txt"

# 4. 创建目录
mkdir -p uploads workspace /var/log/md2word
chown -R www-data:www-data uploads workspace /var/log/md2word

# 5. 创建配置文件
cat > .env.production << EOF
HOST=127.0.0.1
PORT=8003
DEBUG=False
ALLOWED_HOSTS=md2word.com,www.md2word.com
MAX_FILE_SIZE=10485760
FILE_RETENTION_HOURS=2
RATE_LIMIT_PER_DAY=100
LOG_LEVEL=INFO
LOG_FILE=/var/log/md2word/app.log
EOF

# 6. 配置Systemd服务
cat > /etc/systemd/system/md2word.service << EOF
[Unit]
Description=md2word.com FastAPI application
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/srv/md2word
Environment=PATH=/srv/md2word/venv/bin
ExecStart=/srv/md2word/venv/bin/python -m uvicorn backend.main:app --host 127.0.0.1 --port 8003 --workers 2
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 7. 启动服务
systemctl daemon-reload
systemctl enable md2word.service
systemctl start md2word.service

# 8. 配置Nginx
cat > /etc/nginx/sites-available/md2word.com << 'EOF'
server {
    listen 80;
    server_name md2word.com www.md2word.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name md2word.com www.md2word.com;
    
    ssl_certificate /etc/letsencrypt/live/md2word.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/md2word.com/privkey.pem;
    
    client_max_body_size 10M;
    
    location /static/ {
        alias /srv/md2word/static/;
        expires 1y;
    }
    
    location / {
        proxy_pass http://127.0.0.1:8003;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 9. 启用Nginx配置
ln -s /etc/nginx/sites-available/md2word.com /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx

# 10. 配置SSL证书
certbot --nginx -d md2word.com -d www.md2word.com
```

## 🔧 验证部署

```bash
# 检查服务状态
systemctl status md2word.service

# 检查端口监听
netstat -tlnp | grep 8003

# 检查网站访问
curl -I https://md2word.com

# 查看日志
journalctl -u md2word.service -f
```

## ⚠️ 重要注意事项

### 1. 端口配置
- **应用端口**：8003（确保未被占用）
- **Nginx端口**：80, 443（与现有Django网站共用）

### 2. 域名解析
确保域名已正确解析到服务器IP：
```bash
# 检查域名解析
nslookup md2word.com
nslookup www.md2word.com
```

### 3. 防火墙设置
```bash
# 检查防火墙状态
ufw status

# 确保以下端口开放
ufw allow 80/tcp
ufw allow 443/tcp
# 注意：8003端口仅本地访问，不需要对外开放
```

### 4. 与现有Django网站共存
- ✅ 使用不同端口（8003 vs Django默认8000）
- ✅ 使用不同域名（md2word.com vs 现有域名）
- ✅ 独立的Nginx配置文件
- ✅ 独立的Systemd服务

## 📊 服务管理

### 常用命令
```bash
# 启动/停止/重启服务
systemctl start md2word.service
systemctl stop md2word.service
systemctl restart md2word.service

# 查看服务状态
systemctl status md2word.service

# 查看实时日志
journalctl -u md2word.service -f

# 重新加载Nginx
systemctl reload nginx

# 更新代码
cd /srv/md2word
git pull origin master
systemctl restart md2word.service
```

### 监控检查
```bash
# 检查磁盘使用
df -h /srv/md2word

# 检查内存使用
ps aux | grep uvicorn

# 检查文件数量
ls -la /srv/md2word/uploads | wc -l

# 清理过期文件（如需要）
find /srv/md2word/uploads -type f -mtime +1 -delete
```

## 🎯 部署后测试

### 功能测试清单
- [ ] 网站首页正常访问
- [ ] Markdown编辑器正常工作
- [ ] Word文档下载功能正常
- [ ] PDF文档下载功能正常
- [ ] 文件上传功能正常
- [ ] 模板切换功能正常
- [ ] 字体设置功能正常

### 性能测试
```bash
# 简单压力测试
ab -n 100 -c 10 https://md2word.com/

# 检查响应时间
curl -w "@curl-format.txt" -o /dev/null -s https://md2word.com/
```

## 📞 故障排除

### 常见问题
1. **服务启动失败**：检查端口占用和权限
2. **Nginx配置错误**：运行 `nginx -t` 检查语法
3. **SSL证书问题**：确保域名解析正确
4. **文件权限问题**：检查 www-data 用户权限

### 获取帮助
- 查看详细部署文档：`docs/部署指南.md`
- 检查项目日志：`/var/log/md2word/app.log`
- 查看系统日志：`journalctl -u md2word.service`

---

**部署完成后，您的md2word.com网站将与现有Django网站和谐共存，为用户提供优质的Markdown转换服务！**
