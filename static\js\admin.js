/**
 * 管理后台JavaScript - md2word.com
 */

// 全局变量
let currentTab = 'dashboard';
let currentPage = 1;

// 获取认证头
function getAuthHeaders() {
    const token = localStorage.getItem('admin_token');
    if (!token) {
        window.location.href = '/admin/login';
        return null;
    }
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 处理API错误
function handleApiError(error, response) {
    if (response && response.status === 401) {
        localStorage.removeItem('admin_token');
        window.location.href = '/admin/login';
        return;
    }
    console.error('API Error:', error);
}

// 显示标签页
function showTab(tabName) {
    // 隐藏所有标签页内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 移除所有标签按钮的激活状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 显示选中的标签页
    document.getElementById(tabName).classList.add('active');
    event.target.classList.add('active');
    
    currentTab = tabName;
    
    // 加载对应的数据
    switch(tabName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'users':
            loadUsers();
            break;
        case 'conversions':
            loadConversions();
            break;
        case 'feedback':
            loadFeedback();
            break;
    }
}

// 加载仪表板数据
async function loadDashboard() {
    const headers = getAuthHeaders();
    if (!headers) return;
    
    try {
        const response = await fetch('/admin/api/dashboard/stats', { headers });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const stats = await response.json();
        renderDashboard(stats);
    } catch (error) {
        handleApiError(error, response);
        document.getElementById('statsGrid').innerHTML = 
            '<div class="error">加载统计数据失败</div>';
    }
}

// 渲染仪表板
function renderDashboard(stats) {
    const statsGrid = document.getElementById('statsGrid');
    
    statsGrid.innerHTML = `
        <div class="stat-card">
            <div class="icon"><i class="fas fa-file-alt"></i></div>
            <div class="number">${stats.today_conversions || 0}</div>
            <div class="label">今日转换</div>
        </div>
        <div class="stat-card">
            <div class="icon"><i class="fas fa-check-circle"></i></div>
            <div class="number">${stats.today_success || 0}</div>
            <div class="label">今日成功</div>
        </div>
        <div class="stat-card">
            <div class="icon"><i class="fas fa-chart-line"></i></div>
            <div class="number">${stats.total_conversions || 0}</div>
            <div class="label">总转换数</div>
        </div>
        <div class="stat-card">
            <div class="icon"><i class="fas fa-users"></i></div>
            <div class="number">${stats.total_users || 0}</div>
            <div class="label">总用户数</div>
        </div>
        <div class="stat-card">
            <div class="icon"><i class="fas fa-percentage"></i></div>
            <div class="number">${stats.success_rate || 0}%</div>
            <div class="label">成功率</div>
        </div>
        <div class="stat-card">
            <div class="icon"><i class="fas fa-file-word"></i></div>
            <div class="number">${getFormatCount(stats.format_stats, 'docx')}</div>
            <div class="label">Word转换</div>
        </div>
    `;
}

// 获取格式统计
function getFormatCount(formatStats, format) {
    if (!formatStats) return 0;
    const stat = formatStats.find(s => s.format === format);
    return stat ? stat.count : 0;
}

// 加载用户数据
async function loadUsers(page = 1) {
    const headers = getAuthHeaders();
    if (!headers) return;
    
    try {
        const response = await fetch(`/admin/api/users?page=${page}`, { headers });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.json();
        renderUsers(data);
    } catch (error) {
        handleApiError(error, response);
        document.getElementById('usersContent').innerHTML = 
            '<div class="error">加载用户数据失败</div>';
    }
}

// 渲染用户列表
function renderUsers(data) {
    const content = document.getElementById('usersContent');
    
    if (!data.users || data.users.length === 0) {
        content.innerHTML = '<div class="loading">暂无用户数据</div>';
        return;
    }
    
    let html = `
        <table>
            <thead>
                <tr>
                    <th>IP地址</th>
                    <th>国家/城市</th>
                    <th>总转换</th>
                    <th>成功</th>
                    <th>失败</th>
                    <th>最后访问</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    data.users.forEach(user => {
        html += `
            <tr>
                <td>${user.ip_address}</td>
                <td>${user.country || 'Unknown'} / ${user.city || 'Unknown'}</td>
                <td>${user.total_conversions}</td>
                <td class="status-success">${user.successful_conversions}</td>
                <td class="status-failed">${user.failed_conversions}</td>
                <td>${formatDate(user.last_access)}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table>';
    
    // 添加分页
    if (data.pages > 1) {
        html += renderPagination(data.page, data.pages, 'loadUsers');
    }
    
    content.innerHTML = html;
}

// 加载转换记录
async function loadConversions(page = 1) {
    const headers = getAuthHeaders();
    if (!headers) return;
    
    try {
        const response = await fetch(`/admin/api/conversions?page=${page}`, { headers });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.json();
        renderConversions(data);
    } catch (error) {
        handleApiError(error, response);
        document.getElementById('conversionsContent').innerHTML = 
            '<div class="error">加载转换记录失败</div>';
    }
}

// 渲染转换记录
function renderConversions(data) {
    const content = document.getElementById('conversionsContent');
    
    if (!data.conversions || data.conversions.length === 0) {
        content.innerHTML = '<div class="loading">暂无转换记录</div>';
        return;
    }
    
    let html = `
        <table>
            <thead>
                <tr>
                    <th>文件名</th>
                    <th>IP地址</th>
                    <th>格式</th>
                    <th>大小</th>
                    <th>状态</th>
                    <th>耗时</th>
                    <th>时间</th>
                    <th>内容预览</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    data.conversions.forEach(conv => {
        html += `
            <tr>
                <td>${conv.filename}</td>
                <td>${conv.user_ip}</td>
                <td>${conv.output_format.toUpperCase()}</td>
                <td>${formatFileSize(conv.file_size)}</td>
                <td class="status-${conv.status}">${conv.status === 'success' ? '成功' : '失败'}</td>
                <td>${conv.conversion_time ? conv.conversion_time.toFixed(2) + 's' : '-'}</td>
                <td>${formatDate(conv.created_at)}</td>
                <td title="${conv.content_preview || ''}">${truncateText(conv.content_preview, 50)}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table>';
    
    // 添加分页
    if (data.pages > 1) {
        html += renderPagination(data.page, data.pages, 'loadConversions');
    }
    
    content.innerHTML = html;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 截断文本
function truncateText(text, maxLength) {
    if (!text) return '-';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 渲染分页
function renderPagination(currentPage, totalPages, loadFunction) {
    let html = '<div class="pagination">';
    
    // 上一页
    if (currentPage > 1) {
        html += `<button onclick="${loadFunction}(${currentPage - 1})">上一页</button>`;
    }
    
    // 页码
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const activeClass = i === currentPage ? 'active' : '';
        html += `<button class="${activeClass}" onclick="${loadFunction}(${i})">${i}</button>`;
    }
    
    // 下一页
    if (currentPage < totalPages) {
        html += `<button onclick="${loadFunction}(${currentPage + 1})">下一页</button>`;
    }
    
    html += '</div>';
    return html;
}

// 退出登录
function logout() {
    localStorage.removeItem('admin_token');
    window.location.href = '/admin/login';
}

// 加载反馈数据
async function loadFeedback(page = 1) {
    const headers = getAuthHeaders();
    if (!headers) return;

    try {
        const response = await fetch(`/admin/api/feedback?page=${page}`, { headers });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        renderFeedback(data);
    } catch (error) {
        handleApiError(error, response);
        document.getElementById('feedbackContent').innerHTML =
            '<div class="error">加载反馈数据失败</div>';
    }
}

// 渲染反馈列表
function renderFeedback(data) {
    const content = document.getElementById('feedbackContent');

    if (!data.feedbacks || data.feedbacks.length === 0) {
        content.innerHTML = '<div class="loading">暂无反馈数据</div>';
        return;
    }

    let html = `
        <table>
            <thead>
                <tr>
                    <th>类型</th>
                    <th>IP地址</th>
                    <th>内容</th>
                    <th>联系方式</th>
                    <th>状态</th>
                    <th>提交时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;

    data.feedbacks.forEach(feedback => {
        const statusText = {
            'unread': '未读',
            'read': '已读',
            'processed': '已处理'
        };

        const typeText = {
            'suggestion': '建议',
            'bug': '问题',
            'feature': '功能请求',
            'other': '其他'
        };

        html += `
            <tr>
                <td>${typeText[feedback.feedback_type] || feedback.feedback_type}</td>
                <td>${feedback.user_ip}</td>
                <td title="${feedback.content}">${truncateText(feedback.content, 100)}</td>
                <td>${feedback.contact_info || '-'}</td>
                <td class="status-${feedback.status}">${statusText[feedback.status] || feedback.status}</td>
                <td>${formatDate(feedback.created_at)}</td>
                <td>
                    <button onclick="viewFeedback(${feedback.id})" style="padding: 4px 8px; margin-right: 5px; background: #667eea; color: white; border: none; border-radius: 3px; cursor: pointer;">查看</button>
                    ${feedback.status !== 'processed' ? `<button onclick="markProcessed(${feedback.id})" style="padding: 4px 8px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">标记已处理</button>` : ''}
                </td>
            </tr>
        `;
    });

    html += '</tbody></table>';

    // 添加分页
    if (data.pages > 1) {
        html += renderPagination(data.page, data.pages, 'loadFeedback');
    }

    content.innerHTML = html;
}

// 查看反馈详情
function viewFeedback(feedbackId) {
    // 这里可以实现弹窗显示详细信息
    alert('查看反馈功能待实现');
}

// 标记为已处理
async function markProcessed(feedbackId) {
    const headers = getAuthHeaders();
    if (!headers) return;

    try {
        const formData = new FormData();
        formData.append('status', 'processed');

        const response = await fetch(`/admin/api/feedback/${feedbackId}`, {
            method: 'PUT',
            headers: {
                'Authorization': headers.Authorization
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        // 重新加载反馈列表
        loadFeedback();
        alert('已标记为已处理');
    } catch (error) {
        handleApiError(error, response);
        alert('操作失败');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已登录
    const token = localStorage.getItem('admin_token');
    if (!token) {
        window.location.href = '/admin/login';
        return;
    }

    // 加载默认数据
    loadDashboard();
});
