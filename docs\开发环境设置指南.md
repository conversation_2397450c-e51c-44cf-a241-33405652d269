# 开发环境设置指南

## 快速开始（推荐）

### 方案1：使用SQLite（无需安装MySQL）

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **一键启动开发环境**
```bash
python run_dev.py
```

这个脚本会自动：
- 创建SQLite数据库
- 初始化数据表
- 创建默认管理员账号（admin/admin123）
- 启动开发服务器

3. **访问应用**
- 主页：http://localhost:8001/
- 管理后台：http://localhost:8001/admin/login
- 用户名：`admin`，密码：`admin123`

## 方案2：手动设置SQLite

如果您想手动控制设置过程：

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **初始化开发环境**
```bash
python scripts/setup_dev.py
```

3. **启动服务器**
```bash
python -m uvicorn backend.main:app --reload --host 127.0.0.1 --port 8001
```

## 方案3：使用MySQL（完整生产环境模拟）

如果您想在开发环境中使用MySQL：

1. **安装MySQL**
```bash
# Ubuntu/Debian
sudo apt install mysql-server

# macOS
brew install mysql

# Windows
# 下载并安装MySQL Community Server
```

2. **创建数据库**
```sql
CREATE DATABASE md2word CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'md2word'@'localhost' IDENTIFIED BY 'md2word123';
GRANT ALL PRIVILEGES ON md2word.* TO 'md2word'@'localhost';
FLUSH PRIVILEGES;
```

3. **设置环境变量**
```bash
export ENVIRONMENT=production
export DATABASE_URL=mysql+pymysql://md2word:md2word123@localhost:3306/md2word
```

4. **初始化数据库**
```bash
python scripts/init_database.py
python scripts/create_admin.py
```

5. **启动服务器**
```bash
python run.py
```

## 数据库文件位置

### SQLite模式
- 数据库文件：`./md2word.db`
- 可以使用SQLite浏览器查看数据

### MySQL模式
- 数据库：`md2word`
- 可以使用MySQL客户端或phpMyAdmin查看

## 开发工具推荐

### SQLite浏览器
- [DB Browser for SQLite](https://sqlitebrowser.org/)
- [SQLite Studio](https://sqlitestudio.pl/)

### API测试
- [Postman](https://www.postman.com/)
- [Insomnia](https://insomnia.rest/)

## 常见问题

### Q: 如何重置数据库？
**SQLite模式：**
```bash
rm md2word.db
python scripts/setup_dev.py
```

**MySQL模式：**
```bash
mysql -e "DROP DATABASE md2word; CREATE DATABASE md2word CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
python scripts/init_database.py
```

### Q: 如何更改管理员密码？
```bash
python scripts/create_admin.py --username admin --password new_password
```

### Q: 如何查看数据库内容？
**SQLite模式：**
```bash
sqlite3 md2word.db
.tables
SELECT * FROM user_statistics;
```

**MySQL模式：**
```bash
mysql -u md2word -p md2word
SHOW TABLES;
SELECT * FROM user_statistics;
```

### Q: 端口冲突怎么办？
修改配置文件中的端口号，或启动时指定：
```bash
python -m uvicorn backend.main:app --reload --port 8002
```

## 开发模式特性

- ✅ 自动重载（代码修改后自动重启）
- ✅ 详细错误信息
- ✅ SQLite数据库（轻量级）
- ✅ 调试模式开启
- ✅ API文档：http://localhost:8001/docs

## 生产环境部署

开发完成后，使用生产环境部署脚本：
```bash
sudo bash scripts/deploy_production.sh
```

这会自动配置MySQL、Nginx、SSL证书等生产环境组件。
