"""
Pandoc 调试工具 - 用于诊断转换问题
"""

import subprocess
import sys
import os
from pathlib import Path
import tempfile
import shutil
from typing import Dict, Any, Optional

class PandocDebugger:
    """Pandoc 调试器"""
    
    def __init__(self):
        self.debug_info = {}
    
    def check_pandoc_installation(self) -> Dict[str, Any]:
        """检查 Pandoc 安装状态"""
        info = {
            "installed": False,
            "version": None,
            "path": None,
            "error": None
        }
        
        try:
            # 检查 Pandoc 是否可用
            result = subprocess.run(
                ["pandoc", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                info["installed"] = True
                version_lines = result.stdout.split('\n')
                if version_lines:
                    info["version"] = version_lines[0]
                
                # 尝试获取 Pandoc 路径
                try:
                    path_result = subprocess.run(
                        ["where", "pandoc"] if os.name == 'nt' else ["which", "pandoc"],
                        capture_output=True,
                        text=True,
                        timeout=5
                    )
                    if path_result.returncode == 0:
                        info["path"] = path_result.stdout.strip()
                except:
                    pass
            else:
                info["error"] = result.stderr
                
        except FileNotFoundError:
            info["error"] = "Pandoc 未找到，请确保已安装 Pandoc"
        except subprocess.TimeoutExpired:
            info["error"] = "Pandoc 版本检查超时"
        except Exception as e:
            info["error"] = f"检查 Pandoc 时出错: {e}"
        
        return info
    
    def check_system_environment(self) -> Dict[str, Any]:
        """检查系统环境"""
        return {
            "platform": sys.platform,
            "python_version": sys.version,
            "working_directory": str(Path.cwd()),
            "temp_directory": tempfile.gettempdir(),
            "path_env": os.environ.get("PATH", ""),
            "user": os.environ.get("USERNAME", os.environ.get("USER", "unknown"))
        }
    
    def test_simple_conversion(self, test_content: str = "# 测试文档\n\n这是一个测试文档。") -> Dict[str, Any]:
        """测试简单的 Markdown 到 Word 转换"""
        result = {
            "success": False,
            "error": None,
            "command": None,
            "stdout": None,
            "stderr": None,
            "returncode": None,
            "input_file": None,
            "output_file": None,
            "file_sizes": {}
        }
        
        temp_dir = None
        try:
            # 创建临时目录
            temp_dir = Path(tempfile.mkdtemp(prefix="pandoc_test_"))
            
            # 创建测试输入文件
            input_file = temp_dir / "test_input.md"
            output_file = temp_dir / "test_output.docx"
            
            # 写入测试内容
            with open(input_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            result["input_file"] = str(input_file)
            result["output_file"] = str(output_file)
            result["file_sizes"]["input"] = input_file.stat().st_size
            
            # 构建 Pandoc 命令
            cmd = [
                "pandoc",
                str(input_file),
                "-t", "docx",
                "-o", str(output_file)
            ]
            
            result["command"] = " ".join(cmd)
            
            # 执行转换
            process_result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                cwd=str(temp_dir)
            )
            
            result["returncode"] = process_result.returncode
            result["stdout"] = process_result.stdout
            result["stderr"] = process_result.stderr
            
            if process_result.returncode == 0:
                if output_file.exists():
                    result["success"] = True
                    result["file_sizes"]["output"] = output_file.stat().st_size
                else:
                    result["error"] = "Pandoc 返回成功但输出文件不存在"
            else:
                result["error"] = f"Pandoc 返回错误码 {process_result.returncode}: {process_result.stderr}"
                
        except subprocess.TimeoutExpired:
            result["error"] = "转换超时"
        except Exception as e:
            result["error"] = f"测试转换时出错: {e}"
        finally:
            # 清理临时文件
            if temp_dir and temp_dir.exists():
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
        
        return result
    
    def test_with_template(self, template_path: Optional[Path] = None) -> Dict[str, Any]:
        """测试使用模板的转换"""
        if not template_path or not template_path.exists():
            return {"error": "模板文件不存在"}
        
        result = {
            "success": False,
            "error": None,
            "template_info": {
                "path": str(template_path),
                "exists": template_path.exists(),
                "size": template_path.stat().st_size if template_path.exists() else 0
            }
        }
        
        temp_dir = None
        try:
            # 创建临时目录
            temp_dir = Path(tempfile.mkdtemp(prefix="pandoc_template_test_"))
            
            # 创建测试输入文件
            input_file = temp_dir / "test_input.md"
            output_file = temp_dir / "test_output.docx"
            
            test_content = "# 模板测试文档\n\n这是一个使用模板的测试文档。\n\n## 子标题\n\n测试内容。"
            
            with open(input_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            # 构建 Pandoc 命令
            cmd = [
                "pandoc",
                str(input_file),
                "-t", "docx",
                "-o", str(output_file),
                "--reference-doc", str(template_path)
            ]
            
            result["command"] = " ".join(cmd)
            
            # 执行转换
            process_result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                cwd=str(temp_dir)
            )
            
            result["returncode"] = process_result.returncode
            result["stdout"] = process_result.stdout
            result["stderr"] = process_result.stderr
            
            if process_result.returncode == 0 and output_file.exists():
                result["success"] = True
                result["output_size"] = output_file.stat().st_size
            else:
                result["error"] = f"模板转换失败: {process_result.stderr}"
                
        except Exception as e:
            result["error"] = f"模板测试时出错: {e}"
        finally:
            # 清理临时文件
            if temp_dir and temp_dir.exists():
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
        
        return result
    
    def run_full_diagnosis(self, template_path: Optional[Path] = None) -> Dict[str, Any]:
        """运行完整诊断"""
        print("🔍 开始 Pandoc 诊断...")
        
        diagnosis = {
            "pandoc_info": self.check_pandoc_installation(),
            "system_info": self.check_system_environment(),
            "simple_test": self.test_simple_conversion(),
            "template_test": None
        }
        
        # 如果提供了模板路径，测试模板转换
        if template_path:
            diagnosis["template_test"] = self.test_with_template(template_path)
        
        return diagnosis
    
    def print_diagnosis_report(self, diagnosis: Dict[str, Any]):
        """打印诊断报告"""
        print("\n" + "="*80)
        print("📋 PANDOC 诊断报告")
        print("="*80)
        
        # Pandoc 信息
        pandoc_info = diagnosis["pandoc_info"]
        print(f"\n🔧 Pandoc 安装状态:")
        print(f"   已安装: {'✅' if pandoc_info['installed'] else '❌'}")
        if pandoc_info["version"]:
            print(f"   版本: {pandoc_info['version']}")
        if pandoc_info["path"]:
            print(f"   路径: {pandoc_info['path']}")
        if pandoc_info["error"]:
            print(f"   错误: {pandoc_info['error']}")
        
        # 系统信息
        system_info = diagnosis["system_info"]
        print(f"\n💻 系统环境:")
        print(f"   平台: {system_info['platform']}")
        print(f"   Python: {system_info['python_version'].split()[0]}")
        print(f"   工作目录: {system_info['working_directory']}")
        print(f"   临时目录: {system_info['temp_directory']}")
        print(f"   用户: {system_info['user']}")
        
        # 简单转换测试
        simple_test = diagnosis["simple_test"]
        print(f"\n📝 简单转换测试:")
        print(f"   结果: {'✅ 成功' if simple_test['success'] else '❌ 失败'}")
        if simple_test["command"]:
            print(f"   命令: {simple_test['command']}")
        if simple_test["error"]:
            print(f"   错误: {simple_test['error']}")
        if simple_test.get("file_sizes"):
            sizes = simple_test["file_sizes"]
            print(f"   文件大小: 输入={sizes.get('input', 0)} bytes, 输出={sizes.get('output', 0)} bytes")
        
        # 模板转换测试
        if diagnosis["template_test"]:
            template_test = diagnosis["template_test"]
            print(f"\n📄 模板转换测试:")
            print(f"   结果: {'✅ 成功' if template_test['success'] else '❌ 失败'}")
            if template_test.get("template_info"):
                info = template_test["template_info"]
                print(f"   模板: {info['path']} (大小: {info['size']} bytes)")
            if template_test.get("error"):
                print(f"   错误: {template_test['error']}")
        
        print("\n" + "="*80)

# 创建全局调试器实例
pandoc_debugger = PandocDebugger()
