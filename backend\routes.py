"""
API路由 - md2word.com
"""

import asyncio
import json
from pathlib import Path
from typing import Optional
from datetime import datetime

from fastapi import APIRouter, File, UploadFile, HTTPException, Request, Form
from fastapi.responses import FileResponse

from .config import settings
from .utils import (
    get_client_ip, check_rate_limit, increment_rate_limit,
    validate_file, generate_unique_filename, convert_markdown_to_docx,
    convert_markdown_to_pdf, cleanup_old_files
)
from .pandoc_debug import pandoc_debugger

router = APIRouter()

@router.post("/convert")
async def convert_file(
    request: Request,
    file: UploadFile = File(...),
    reference: Optional[UploadFile] = File(None),
    config: Optional[str] = Form(None),
    format: Optional[str] = Form("docx")
):
    """转换Markdown文件到Word或PDF"""
    
    # 限流检查已禁用
    client_ip = get_client_ip(request)
    # if not check_rate_limit(client_ip):
    #     raise HTTPException(
    #         status_code=429,
    #         detail="Rate limit exceeded. Maximum 5 conversions per day."
    #     )
    
    # 验证文件
    if not validate_file(file):
        raise HTTPException(
            status_code=400, 
            detail="Invalid file type. Only .md, .markdown, .txt files are allowed."
        )
    
    # 检查文件大小
    content = await file.read()
    if len(content) > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=413,
            detail="File too large. Maximum size is 10MB."
        )

    # 重置文件指针
    await file.seek(0)
    
    # 验证输出格式
    output_format = format.lower() if format else "docx"
    if output_format not in ["docx", "pdf"]:
        raise HTTPException(
            status_code=400,
            detail="Invalid format. Only 'docx' and 'pdf' are supported."
        )

    # 生成唯一文件名
    input_filename = generate_unique_filename(file.filename, "_input")
    output_extension = f".{output_format}"
    output_filename = generate_unique_filename(file.filename, "_output").replace(
        Path(file.filename).suffix, output_extension
    )

    input_path = settings.UPLOAD_DIR / input_filename
    output_path = settings.UPLOAD_DIR / output_filename
    reference_path = None
    
    try:
        # 🔍 调试：打印请求信息
        print(f"\n{'='*60}")
        print(f"🔍 [DEBUG] 收到转换请求")
        print(f"📁 原始文件名: {file.filename}")
        print(f"📄 文件类型: {file.content_type}")
        print(f"📋 输出格式: {output_format}")
        print(f"⚙️  原始配置: {config}")
        print(f"{'='*60}")

        # 解析用户配置
        user_config = {}
        if config:
            try:
                user_config = json.loads(config)
                print(f"🔧 [DEBUG] 解析后配置: {user_config}")
            except json.JSONDecodeError as e:
                print(f"❌ [DEBUG] 配置解析错误: {e}")

        # 保存输入文件
        with open(input_path, "wb") as f:
            f.write(content)

        # 处理参考模板
        if reference:
            reference_content = await reference.read()
            reference_filename = generate_unique_filename(reference.filename, "_reference")
            reference_path = settings.UPLOAD_DIR / reference_filename
            with open(reference_path, "wb") as f:
                f.write(reference_content)

        # 执行转换
        if output_format == "pdf":
            print("📝 使用Markdown到PDF转换")
            success = await convert_markdown_to_pdf(input_path, output_path, reference_path, user_config)
        else:
            print("📝 使用标准Markdown到Word转换")
            success = await convert_markdown_to_docx(input_path, output_path, reference_path, user_config)

        if not success:
            # 转换失败时运行诊断
            print("\n🔍 转换失败，开始运行 Pandoc 诊断...")
            diagnosis = pandoc_debugger.run_full_diagnosis(None)
            pandoc_debugger.print_diagnosis_report(diagnosis)

            raise HTTPException(status_code=500, detail="Conversion failed")

        # 限流计数已禁用
        # increment_rate_limit(client_ip)

        # 启动清理任务
        asyncio.create_task(cleanup_old_files())

        # 返回转换后的文件
        if output_format == "pdf":
            return FileResponse(
                path=output_path,
                filename=f"{Path(file.filename).stem}.pdf",
                media_type="application/pdf"
            )
        else:
            return FileResponse(
                path=output_path,
                filename=f"{Path(file.filename).stem}.docx",
                media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            )
        
    except Exception as e:
        # 详细错误日志记录
        import traceback
        error_details = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "traceback": traceback.format_exc(),
            "file_info": {
                "input_file": file.filename if file else "None",
                "input_path": str(input_path) if 'input_path' in locals() else "None",
                "output_path": str(output_path) if 'output_path' in locals() else "None",
                "reference_path": str(reference_path) if reference_path else "None"
            },
            "config": user_config if 'user_config' in locals() else {}
        }

        print(f"\n{'='*80}")
        print(f"❌ 转换错误详情:")
        print(f"   错误类型: {error_details['error_type']}")
        print(f"   错误消息: {error_details['error_message']}")
        print(f"   输入文件: {error_details['file_info']['input_file']}")
        print(f"   输入路径: {error_details['file_info']['input_path']}")
        print(f"   输出路径: {error_details['file_info']['output_path']}")
        print(f"   参考路径: {error_details['file_info']['reference_path']}")
        print(f"   用户配置: {error_details['config']}")
        print(f"   完整堆栈:")
        print(f"{error_details['traceback']}")
        print(f"{'='*80}\n")

        # 清理临时文件
        for path in [input_path, output_path, reference_path]:
            if path and path.exists():
                try:
                    path.unlink()
                    print(f"🧹 清理临时文件: {path}")
                except Exception as cleanup_error:
                    print(f"⚠️  清理文件失败 {path}: {cleanup_error}")

        # 根据错误类型提供更友好的错误消息
        if "WinError 233" in str(e) or "管道的另一端上无任何进程" in str(e):
            error_msg = "文档转换过程中出现进程通信错误，这可能是由于文件路径包含特殊字符或系统权限问题导致的。请尝试使用英文文件名或联系管理员。"
        elif "TimeoutExpired" in str(e) or "timeout" in str(e).lower():
            error_msg = "文档转换超时，请尝试转换较小的文件或稍后重试。"
        elif "FileNotFoundError" in str(e):
            error_msg = "转换工具未找到，请联系管理员检查系统配置。"
        elif "PermissionError" in str(e):
            error_msg = "文件权限错误，请检查文件是否被其他程序占用。"
        else:
            error_msg = f"转换过程中出现错误: {str(e)}"

        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": settings.APP_VERSION
    }

@router.get("/debug/pandoc")
async def debug_pandoc():
    """Pandoc 调试端点"""
    try:
        # 运行完整诊断
        diagnosis = pandoc_debugger.run_full_diagnosis(None)

        # 打印到控制台
        pandoc_debugger.print_diagnosis_report(diagnosis)

        # 返回诊断结果
        return {
            "status": "diagnosis_complete",
            "pandoc_installed": diagnosis["pandoc_info"]["installed"],
            "pandoc_version": diagnosis["pandoc_info"]["version"],
            "simple_test_success": diagnosis["simple_test"]["success"],
            "template_test_success": diagnosis["template_test"]["success"] if diagnosis["template_test"] else None,
            "errors": {
                "pandoc_error": diagnosis["pandoc_info"]["error"],
                "simple_test_error": diagnosis["simple_test"]["error"],
                "template_test_error": diagnosis["template_test"]["error"] if diagnosis["template_test"] else None
            },
            "full_diagnosis": diagnosis
        }
    except Exception as e:
        return {
            "status": "diagnosis_failed",
            "error": str(e)
        }
