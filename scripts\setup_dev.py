#!/usr/bin/env python3
"""
开发环境快速设置脚本 - md2word.com
使用SQLite数据库，无需安装MySQL
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置开发环境
os.environ["ENVIRONMENT"] = "development"

from backend.database import init_database, test_connection
from backend.auth import create_admin_user


def main():
    print("=" * 50)
    print("md2word.com 开发环境快速设置")
    print("=" * 50)
    
    print("使用SQLite数据库，无需安装MySQL")
    
    # 测试数据库连接
    print("\n正在初始化SQLite数据库...")
    if not test_connection():
        print("❌ 数据库连接失败！")
        return 1
    
    print("✅ SQLite数据库连接成功")
    
    # 初始化数据库
    try:
        init_database()
        print("✅ 数据库表创建成功")
        
        # 显示创建的表
        print("\n已创建的数据表：")
        print("- user_statistics (用户统计)")
        print("- conversion_logs (转换记录)")
        print("- admin_users (管理员用户)")
        print("- user_feedback (用户反馈)")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return 1
    
    # 创建默认管理员账号
    print("\n正在创建默认管理员账号...")
    success = create_admin_user("admin", "admin123")
    
    if success:
        print("✅ 默认管理员账号创建成功")
        print("用户名: admin")
        print("密码: admin123")
    else:
        print("⚠️  管理员账号已存在或创建失败")
    
    # 创建开发环境配置文件
    env_file = project_root / ".env.dev"
    with open(env_file, 'w') as f:
        f.write("# 开发环境配置\n")
        f.write("ENVIRONMENT=development\n")
        f.write("DEBUG=True\n")
        f.write("DATABASE_URL=sqlite:///./md2word.db\n")
        f.write("SECRET_KEY=dev-secret-key-change-in-production\n")
        f.write("ADMIN_USERNAME=admin\n")
        f.write("ADMIN_PASSWORD=admin123\n")
    
    print(f"\n✅ 开发环境配置已保存到 {env_file}")
    
    print("\n" + "=" * 50)
    print("开发环境设置完成！")
    print("=" * 50)
    print("数据库文件: ./md2word.db")
    print("管理员账号: admin / admin123")
    print("管理后台: http://localhost:8001/admin/login")
    print("")
    print("启动开发服务器:")
    print("python -m uvicorn backend.main:app --reload --host 127.0.0.1 --port 8001")
    print("")
    print("或使用现有的启动脚本:")
    print("python run.py")
    print("=" * 50)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
