"""
pytest 配置文件
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from fastapi.testclient import TestClient

from backend.main import app
from backend.config import settings

@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)

@pytest.fixture
def temp_upload_dir():
    """临时上传目录"""
    temp_dir = Path(tempfile.mkdtemp())
    original_upload_dir = settings.UPLOAD_DIR
    settings.UPLOAD_DIR = temp_dir
    
    yield temp_dir
    
    # 清理
    settings.UPLOAD_DIR = original_upload_dir
    shutil.rmtree(temp_dir, ignore_errors=True)

@pytest.fixture
def sample_markdown_file():
    """示例Markdown文件"""
    content = """# 测试文档

这是一个测试文档。

## 功能特性

- **粗体文本**
- *斜体文本*
- `行内代码`

### 代码块

```python
def hello():
    print("Hello, World!")
```

### 表格

| 列1 | 列2 |
|-----|-----|
| 值1 | 值2 |

### 链接

[GitHub](https://github.com)
"""
    return content.encode('utf-8')

@pytest.fixture
def large_markdown_file():
    """大型Markdown文件（用于测试文件大小限制）"""
    content = "# 大文件测试\n\n" + "这是一行很长的文本。" * 100000
    return content.encode('utf-8')
