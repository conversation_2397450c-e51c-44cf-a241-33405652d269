"""
工具函数测试
"""

import pytest
from unittest.mock import Mock, patch
from pathlib import Path
from fastapi import UploadFile

from backend.utils import (
    validate_file, generate_unique_filename, check_rate_limit,
    increment_rate_limit, check_pandoc_availability
)

def test_validate_file_valid():
    """测试有效文件验证"""
    mock_file = Mock(spec=UploadFile)
    mock_file.filename = "test.md"
    
    assert validate_file(mock_file) == True

def test_validate_file_invalid_extension():
    """测试无效文件扩展名"""
    mock_file = Mock(spec=UploadFile)
    mock_file.filename = "test.pdf"
    
    assert validate_file(mock_file) == False

def test_validate_file_no_filename():
    """测试没有文件名的文件"""
    mock_file = Mock(spec=UploadFile)
    mock_file.filename = None
    
    assert validate_file(mock_file) == False

def test_generate_unique_filename():
    """测试唯一文件名生成"""
    filename1 = generate_unique_filename("test.md")
    filename2 = generate_unique_filename("test.md")
    
    # 应该生成不同的文件名
    assert filename1 != filename2
    
    # 应该保持原始扩展名
    assert filename1.endswith(".md")
    assert filename2.endswith(".md")

def test_generate_unique_filename_with_suffix():
    """测试带后缀的唯一文件名生成"""
    filename = generate_unique_filename("test.md", "_output")
    assert "_output.md" in filename

def test_rate_limit_functions():
    """测试限流功能"""
    test_ip = "*************"
    
    # 初始状态应该允许请求
    assert check_rate_limit(test_ip) == True
    
    # 增加计数
    for i in range(5):
        increment_rate_limit(test_ip)
    
    # 达到限制后应该拒绝
    assert check_rate_limit(test_ip) == False

@patch('subprocess.run')
def test_check_pandoc_availability_success(mock_run):
    """测试Pandoc可用性检查 - 成功"""
    mock_run.return_value.returncode = 0
    
    assert check_pandoc_availability() == True
    mock_run.assert_called_once_with(["pandoc", "--version"], capture_output=True)

@patch('subprocess.run')
def test_check_pandoc_availability_failure(mock_run):
    """测试Pandoc可用性检查 - 失败"""
    mock_run.side_effect = Exception("Command not found")
    
    assert check_pandoc_availability() == False
