/**
 * md2word.com 前端应用
 */

// 当前语言
let currentLanguage = 'en';

// 语言切换功能
function toggleLanguage() {
    currentLanguage = currentLanguage === 'en' ? 'zh' : 'en';
    updateLanguage();
}

function updateLanguage() {
    const elements = document.querySelectorAll('[data-en][data-zh]');
    elements.forEach(element => {
        const text = element.getAttribute(`data-${currentLanguage}`);
        if (text) {
            element.textContent = text;
        }
    });
    
    // 更新语言切换按钮
    const langText = document.getElementById('lang-text');
    langText.textContent = currentLanguage === 'en' ? '中文' : 'English';
    
    // 更新HTML lang属性
    document.documentElement.lang = currentLanguage === 'en' ? 'en' : 'zh-CN';
}

// 文件上传和转换
async function uploadFile() {
    const fileInput = document.getElementById('file-input');
    const referenceInput = document.getElementById('reference-input');
    
    if (!fileInput.files[0]) {
        showError(currentLanguage === 'en' ? 'Please select a Markdown file' : '请选择一个 Markdown 文件');
        return;
    }
    
    // 验证文件类型
    const file = fileInput.files[0];
    const allowedExtensions = ['.md', '.markdown', '.txt'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedExtensions.includes(fileExtension)) {
        showError(currentLanguage === 'en' ? 
            'Invalid file type. Only .md, .markdown, .txt files are allowed.' :
            '无效的文件类型。只允许 .md, .markdown, .txt 文件。');
        return;
    }
    
    // 验证文件大小
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        showError(currentLanguage === 'en' ? 
            'File too large. Maximum size is 10MB.' :
            '文件过大。最大大小为 10MB。');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    if (referenceInput.files[0]) {
        formData.append('reference', referenceInput.files[0]);
    }
    
    // 显示进度
    showProgress();
    
    try {
        const response = await fetch('/convert', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            
            showResult(url, file.name);
        } else {
            const errorText = await response.text();
            let errorMessage = errorText;
            
            // 处理常见错误的本地化
            if (response.status === 413) {
                errorMessage = currentLanguage === 'en' ? 
                    'File too large. Maximum size is 10MB.' :
                    '文件过大。最大大小为 10MB。';
            } else if (response.status === 400) {
                errorMessage = currentLanguage === 'en' ? 
                    'Invalid file type. Only .md, .markdown, .txt files are allowed.' :
                    '无效的文件类型。只允许 .md, .markdown, .txt 文件。';
            }
            
            showError(errorMessage);
        }
    } catch (error) {
        console.error('Upload error:', error);
        showError(currentLanguage === 'en' ? 
            'Network error. Please check your connection and try again.' :
            '网络错误。请检查您的连接并重试。');
    }
}

function showProgress() {
    document.getElementById('upload-section').classList.add('hidden');
    document.getElementById('progress-section').classList.remove('hidden');
    document.getElementById('result-section').classList.add('hidden');
    document.getElementById('error-section').classList.add('hidden');
    
    // 模拟进度条动画
    const progressBar = document.getElementById('progress-bar');
    let width = 0;
    const interval = setInterval(() => {
        width += Math.random() * 30;
        if (width >= 90) {
            width = 90;
            clearInterval(interval);
        }
        progressBar.style.width = width + '%';
    }, 200);
}

function showResult(downloadUrl, originalFilename) {
    document.getElementById('upload-section').classList.add('hidden');
    document.getElementById('progress-section').classList.add('hidden');
    document.getElementById('result-section').classList.remove('hidden');
    document.getElementById('error-section').classList.add('hidden');
    
    // 完成进度条
    document.getElementById('progress-bar').style.width = '100%';
    
    // 设置下载按钮
    document.getElementById('download-btn').onclick = () => {
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = originalFilename.replace(/\.(md|markdown|txt)$/i, '.docx');
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    };
}

function showError(message) {
    document.getElementById('upload-section').classList.add('hidden');
    document.getElementById('progress-section').classList.add('hidden');
    document.getElementById('result-section').classList.add('hidden');
    document.getElementById('error-section').classList.remove('hidden');
    
    document.getElementById('error-text').textContent = message;
}

function resetForm() {
    document.getElementById('upload-section').classList.remove('hidden');
    document.getElementById('progress-section').classList.add('hidden');
    document.getElementById('result-section').classList.add('hidden');
    document.getElementById('error-section').classList.add('hidden');
    
    // 重置文件输入
    document.getElementById('file-input').value = '';
    document.getElementById('reference-input').value = '';
    
    // 重置进度条
    document.getElementById('progress-bar').style.width = '0%';
}

// 拖拽上传功能
function setupDragAndDrop() {
    const uploadArea = document.querySelector('.upload-area');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    
    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });
    
    uploadArea.addEventListener('drop', handleDrop, false);
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    function highlight(e) {
        uploadArea.classList.add('dragover');
    }
    
    function unhighlight(e) {
        uploadArea.classList.remove('dragover');
    }
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            document.getElementById('file-input').files = files;
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    setupDragAndDrop();
    updateLanguage();
    
    // 检测浏览器语言
    const browserLang = navigator.language || navigator.userLanguage;
    if (browserLang.startsWith('zh')) {
        currentLanguage = 'zh';
        updateLanguage();
    }
});
