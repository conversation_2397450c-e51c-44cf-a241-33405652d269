#!/bin/bash

# md2word.com 生产环境部署脚本
# 使用方法: bash scripts/deploy_production.sh

set -e  # 遇到错误立即退出

echo "=========================================="
echo "md2word.com 生产环境部署脚本"
echo "=========================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    echo "使用方法: sudo bash scripts/deploy_production.sh"
    exit 1
fi

# 配置变量
PROJECT_NAME="md2word"
PROJECT_DIR="/srv/md2word"
DOMAIN="md2word.com"
ALT_DOMAIN="www.md2word.com"
APP_PORT="8001"
DB_NAME="md2word"
DB_USER="md2word"
DB_PASSWORD="md2word123"

echo "配置信息："
echo "项目目录: $PROJECT_DIR"
echo "域名: $DOMAIN, $ALT_DOMAIN"
echo "应用端口: $APP_PORT"
echo "数据库: $DB_NAME"

# 1. 更新系统
echo -e "\n1. 更新系统包..."
apt update && apt upgrade -y

# 2. 安装必要的软件包
echo -e "\n2. 安装必要软件包..."
apt install -y python3 python3-pip python3-venv nginx mysql-server certbot python3-certbot-nginx pandoc git curl

# 3. 配置MySQL
echo -e "\n3. 配置MySQL数据库..."
systemctl start mysql
systemctl enable mysql

# 创建数据库和用户
mysql -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
mysql -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';"
mysql -e "FLUSH PRIVILEGES;"

echo "✅ MySQL配置完成"

# 4. 创建项目目录
echo -e "\n4. 设置项目目录..."
mkdir -p $PROJECT_DIR
cd $PROJECT_DIR

# 如果项目已存在，备份
if [ -d "markdowntoword" ]; then
    echo "备份现有项目..."
    mv markdowntoword markdowntoword_backup_$(date +%Y%m%d_%H%M%S)
fi

# 5. 克隆或复制项目代码
echo -e "\n5. 部署项目代码..."
# 这里假设代码已经在当前目录，实际部署时可能需要从git克隆
# git clone https://github.com/your-repo/markdowntoword.git
cp -r /path/to/your/project ./markdowntoword || echo "请手动复制项目代码到 $PROJECT_DIR/markdowntoword"

cd markdowntoword

# 6. 设置Python虚拟环境
echo -e "\n6. 设置Python虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install --upgrade pip
pip install -r requirements.txt

# 7. 配置环境变量
echo -e "\n7. 配置环境变量..."
cat > .env << EOF
# 生产环境配置
DEBUG=False
HOST=127.0.0.1
PORT=$APP_PORT

# 数据库配置
DATABASE_URL=mysql+pymysql://$DB_USER:$DB_PASSWORD@localhost:3306/$DB_NAME

# 安全配置
SECRET_KEY=$(openssl rand -hex 32)

# 管理员配置（稍后通过脚本设置）
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
EOF

# 8. 初始化数据库
echo -e "\n8. 初始化数据库..."
python scripts/init_database.py

# 9. 创建管理员账号
echo -e "\n9. 创建管理员账号..."
echo "请设置管理员账号："
python scripts/create_admin.py

# 10. 配置Nginx
echo -e "\n10. 配置Nginx..."
cat > /etc/nginx/sites-available/$PROJECT_NAME << EOF
server {
    listen 80;
    server_name $DOMAIN $ALT_DOMAIN;

    # 静态文件
    location /static/ {
        alias $PROJECT_DIR/markdowntoword/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # SEO文件
    location = /robots.txt {
        alias $PROJECT_DIR/markdowntoword/static/robots.txt;
    }

    location = /sitemap.xml {
        alias $PROJECT_DIR/markdowntoword/static/sitemap.xml;
    }

    # 应用代理
    location / {
        proxy_pass http://127.0.0.1:$APP_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 文件上传大小限制
        client_max_body_size 10M;
    }
}
EOF

# 启用站点
ln -sf /etc/nginx/sites-available/$PROJECT_NAME /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 测试Nginx配置
nginx -t

# 11. 配置SSL证书
echo -e "\n11. 配置SSL证书..."
systemctl reload nginx
certbot --nginx -d $DOMAIN -d $ALT_DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN

# 12. 创建systemd服务
echo -e "\n12. 创建systemd服务..."
cat > /etc/systemd/system/$PROJECT_NAME.service << EOF
[Unit]
Description=md2word.com FastAPI Application
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=$PROJECT_DIR/markdowntoword
Environment=PATH=$PROJECT_DIR/markdowntoword/venv/bin
ExecStart=$PROJECT_DIR/markdowntoword/venv/bin/python -m uvicorn backend.main:app --host 127.0.0.1 --port $APP_PORT
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 设置权限
chown -R www-data:www-data $PROJECT_DIR
chmod +x scripts/*.py

# 启动服务
systemctl daemon-reload
systemctl enable $PROJECT_NAME
systemctl start $PROJECT_NAME

# 启动Nginx
systemctl enable nginx
systemctl restart nginx

# 13. 验证部署
echo -e "\n13. 验证部署..."
sleep 5

if systemctl is-active --quiet $PROJECT_NAME; then
    echo "✅ 应用服务运行正常"
else
    echo "❌ 应用服务启动失败"
    systemctl status $PROJECT_NAME
fi

if systemctl is-active --quiet nginx; then
    echo "✅ Nginx运行正常"
else
    echo "❌ Nginx启动失败"
    systemctl status nginx
fi

# 测试HTTP响应
if curl -s -o /dev/null -w "%{http_code}" http://localhost:$APP_PORT/health | grep -q "200"; then
    echo "✅ 应用健康检查通过"
else
    echo "❌ 应用健康检查失败"
fi

echo -e "\n=========================================="
echo "部署完成！"
echo "=========================================="
echo "网站地址: https://$DOMAIN"
echo "管理后台: https://$DOMAIN/admin/login"
echo "健康检查: https://$DOMAIN/health"
echo ""
echo "服务管理命令："
echo "启动服务: systemctl start $PROJECT_NAME"
echo "停止服务: systemctl stop $PROJECT_NAME"
echo "重启服务: systemctl restart $PROJECT_NAME"
echo "查看日志: journalctl -u $PROJECT_NAME -f"
echo ""
echo "Nginx管理："
echo "重启Nginx: systemctl restart nginx"
echo "查看Nginx状态: systemctl status nginx"
echo ""
echo "SSL证书续期："
echo "certbot renew --dry-run"
echo "=========================================="
