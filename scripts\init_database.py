#!/usr/bin/env python3
"""
数据库初始化脚本 - md2word.com
使用方法：
    python scripts/init_database.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.database import init_database, test_connection, create_tables
from backend.config import settings


def main():
    print("=" * 50)
    print("md2word.com 数据库初始化工具")
    print("=" * 50)
    
    print(f"数据库URL: {settings.DATABASE_URL}")
    
    # 测试数据库连接
    print("\n正在测试数据库连接...")
    if not test_connection():
        print("❌ 数据库连接失败！")
        print("\n请检查以下配置：")
        print("1. MySQL服务是否正在运行")
        print("2. 数据库是否已创建")
        print("3. 用户名和密码是否正确")
        print("4. 数据库权限是否足够")
        print("\n创建数据库的SQL命令：")
        print("CREATE DATABASE md2word CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
        print("CREATE USER 'md2word'@'localhost' IDENTIFIED BY 'md2word123';")
        print("GRANT ALL PRIVILEGES ON md2word.* TO 'md2word'@'localhost';")
        print("FLUSH PRIVILEGES;")
        return 1
    
    print("✅ 数据库连接成功")
    
    # 创建数据表
    print("\n正在创建数据表...")
    try:
        create_tables()
        print("✅ 数据表创建成功")
        
        # 显示创建的表
        print("\n已创建的数据表：")
        print("- user_statistics (用户统计)")
        print("- conversion_logs (转换记录)")
        print("- admin_users (管理员用户)")
        print("- user_feedback (用户反馈)")
        
    except Exception as e:
        print(f"❌ 创建数据表失败: {e}")
        return 1
    
    print("\n数据库初始化完成！")
    print("\n下一步：")
    print("1. 运行 'python scripts/create_admin.py' 创建管理员账号")
    print("2. 启动应用服务器")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
