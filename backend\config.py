"""
配置文件 - md2word.com
"""

import os
from pathlib import Path
from typing import List

class Settings:
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "md2word.com"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "Convert Markdown files to Word documents"
    
    # 服务器配置
    HOST: str = "127.0.0.1"
    PORT: int = 8001
    DEBUG: bool = True
    
    # 文件配置
    UPLOAD_DIR: Path = Path("workspace/uploads")
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = [".md", ".markdown", ".txt"]
    FILE_RETENTION_HOURS: int = 2
    
    # 限流配置 - 已禁用
    RATE_LIMIT_PER_DAY: int = 999999
    
    # Pandoc配置
    PANDOC_TIMEOUT: int = 30  # 秒
    
    # CORS配置
    CORS_ORIGINS: List[str] = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]
    
    def __init__(self):
        # 确保上传目录存在
        self.UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# 全局配置实例
settings = Settings()
