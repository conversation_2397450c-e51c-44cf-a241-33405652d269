"""
配置文件 - md2word.com
"""

import os
from pathlib import Path
from typing import List

class Settings:
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "md2word.com"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "Convert Markdown files to Word documents"
    
    # 服务器配置
    HOST: str = "127.0.0.1"
    PORT: int = 8001
    DEBUG: bool = True
    
    # 文件配置
    UPLOAD_DIR: Path = Path("workspace/uploads")
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = [".md", ".markdown", ".txt"]
    FILE_RETENTION_HOURS: int = 2
    
    # 限流配置 - 已禁用
    RATE_LIMIT_PER_DAY: int = 999999
    
    # Pandoc配置
    PANDOC_TIMEOUT: int = 30  # 秒
    
    # CORS配置
    CORS_ORIGINS: List[str] = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]

    # 数据库配置
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL",
        "mysql+pymysql://md2word:md2word123@localhost:3306/md2word"
    )

    # JWT配置
    SECRET_KEY: str = os.getenv(
        "SECRET_KEY",
        "your-secret-key-change-in-production"
    )
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_MINUTES: int = 60 * 24  # 24小时

    # 地理位置API配置
    GEOLOCATION_API_URL: str = "http://ip-api.com/json/"

    # 管理员配置
    ADMIN_USERNAME: str = os.getenv("ADMIN_USERNAME", "admin")
    ADMIN_PASSWORD: str = os.getenv("ADMIN_PASSWORD", "admin123")
    
    def __init__(self):
        # 确保上传目录存在
        self.UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# 全局配置实例
settings = Settings()
