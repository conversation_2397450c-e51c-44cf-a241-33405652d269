<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>目录功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        textarea { width: 100%; height: 200px; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>目录功能测试</h1>
    
    <div class="test-section">
        <h2>测试1：基本目录解析</h2>
        <textarea id="test-content"># 第一章
这是第一章的内容

## 1.1 小节
这是小节内容

### 1.1.1 子小节
这是子小节内容

# 第二章
这是第二章的内容

## 2.1 另一个小节
内容...</textarea>
        <br>
        <button onclick="testParse()">测试解析</button>
        <div class="result" id="parse-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2：正则表达式测试</h2>
        <button onclick="testRegex()">测试正则</button>
        <div class="result" id="regex-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3：实际编辑器内容</h2>
        <button onclick="testEditorContent()">测试编辑器</button>
        <div class="result" id="editor-result"></div>
    </div>

    <script>
        // 复制目录解析函数
        function parseTableOfContents(content) {
            const lines = content.split('\n');
            const toc = [];
            const headingRegex = /^(#{1,6})\s+(.+)$/;
            
            console.log('解析目录 - 总行数:', lines.length);
            
            lines.forEach((line, index) => {
                const trimmedLine = line.trim();
                
                // 检查是否以#开头
                if (trimmedLine.startsWith('#')) {
                    console.log(`第${index}行可能是标题: "${trimmedLine}"`);
                }
                
                const match = trimmedLine.match(headingRegex);
                if (match) {
                    const level = match[1].length;
                    const title = match[2].trim();
                    const id = `heading-${index}-${title.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase()}`;
                    
                    console.log(`找到标题 - 级别:${level}, 标题:"${title}"`);
                    
                    toc.push({
                        level,
                        title,
                        id,
                        lineNumber: index,
                        element: null
                    });
                }
            });
            
            console.log('最终解析结果:', toc);
            return toc;
        }
        
        function testParse() {
            const content = document.getElementById('test-content').value;
            console.log('测试内容:', content);
            
            const result = parseTableOfContents(content);
            
            document.getElementById('parse-result').innerHTML = `
                <h3>解析结果：</h3>
                <p>找到 ${result.length} 个标题</p>
                <ul>
                    ${result.map(item => `<li>级别${item.level}: ${item.title}</li>`).join('')}
                </ul>
            `;
        }
        
        function testRegex() {
            const headingRegex = /^(#{1,6})\s+(.+)$/;
            const testLines = [
                '# 标题1',
                '## 标题2',
                '### 标题3',
                '#### 标题4',
                '##### 标题5',
                '###### 标题6',
                '####### 无效标题',
                '# ',
                '#标题无空格',
                '  # 前面有空格',
                'not a heading',
                ''
            ];
            
            let results = [];
            testLines.forEach((line, index) => {
                const trimmed = line.trim();
                const match = trimmed.match(headingRegex);
                results.push(`${index}: "${line}" -> ${match ? `匹配: 级别${match[1].length}, 标题"${match[2]}"` : '不匹配'}`);
            });
            
            document.getElementById('regex-result').innerHTML = `
                <h3>正则表达式测试结果：</h3>
                <pre>${results.join('\n')}</pre>
            `;
        }
        
        function testEditorContent() {
            // 尝试从主页面获取编辑器内容
            try {
                const editor = parent.document.getElementById('markdown-editor') || 
                              window.opener?.document.getElementById('markdown-editor');
                
                if (editor) {
                    const content = editor.value;
                    const result = parseTableOfContents(content);
                    
                    document.getElementById('editor-result').innerHTML = `
                        <h3>编辑器内容测试：</h3>
                        <p>内容长度: ${content.length}</p>
                        <p>找到标题: ${result.length} 个</p>
                        <h4>前200字符:</h4>
                        <pre>${content.substring(0, 200)}</pre>
                        <h4>解析结果:</h4>
                        <ul>
                            ${result.map(item => `<li>级别${item.level}: ${item.title}</li>`).join('')}
                        </ul>
                    `;
                } else {
                    document.getElementById('editor-result').innerHTML = `
                        <p style="color: red;">无法访问编辑器元素</p>
                    `;
                }
            } catch (error) {
                document.getElementById('editor-result').innerHTML = `
                    <p style="color: red;">错误: ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
