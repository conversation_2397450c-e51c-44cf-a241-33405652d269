# md2word.com

![md2word.com](https://via.placeholder.com/800x200/667eea/ffffff?text=md2word.com)

一款快速、稳定、基于浏览器的在线服务，将 Markdown 文件转换为 Microsoft Word（.docx）和 PDF 格式，同时完整保留标题层级、代码高亮、图片、表格、Front‑matter 元数据等格式。

## 🌟 特性

### 核心功能
- **现代化编辑器**：三栏布局（编辑器 | 目录 | 预览），可拖拽调整面板大小
- **实时预览**：Markdown 实时渲染，代码高亮，数学公式支持
- **智能目录**：自动提取 H1-H6 标题，点击跳转，可调节字体大小
- **双格式输出**：支持 Word (.docx) 和 PDF 格式输出，满足不同需求
- **专业模板**：内置默认、商务、学术三种专业模板，自动生成封面图
- **字体定制**：中英文字体分别设置，支持 10 种常用字体，字体大小可调
- **目录生成**：自动生成 Word 原生目录，支持 1-6 级深度控制

### 技术特性
- **高性能**：基于 FastAPI + Pandoc + ReportLab，转换速度快，模板生成高效
- **高质量**：生成专业级 Word 和 PDF 文档，完整保留 Markdown 格式
- **高可用**：完善的错误处理、日志记录、文件清理机制
- **高扩展**：模块化架构，易于扩展新功能和模板

### 用户体验
- **简单易用**：拖放上传，一键转换，直观的配置界面
- **格式保留**：完整保留标题层级、代码高亮、表格、图片等格式
- **多语言**：支持英文和简体中文界面
- **隐私保护**：文件 2 小时后自动删除，本地处理保护隐私
- **免费使用**：每个 IP 每天可免费转换 5 次

## 🚀 快速开始

### 环境要求

- Python 3.11+
- Pandoc 3.2+

### 安装步骤

1. 克隆仓库

```bash
git clone https://github.com/yourusername/md2word.com.git
cd md2word.com
```

2. 创建虚拟环境

```bash
python -m venv venv
```

3. 激活虚拟环境

Windows:
```bash
venv\Scripts\activate
```

Linux/macOS:
```bash
source venv/bin/activate
```

4. 安装依赖

```bash
pip install -r requirements.txt
```

5. 确保 Pandoc 已安装

Windows:
- 从 [Pandoc 官网](https://pandoc.org/installing.html) 下载并安装
- 确保 Pandoc 已添加到系统 PATH

Linux:
```bash
sudo apt-get install pandoc
```

macOS:
```bash
brew install pandoc
```

6. 启动应用

**方法一：使用主启动脚本**
```bash
python run.py
```

**方法二：使用简化启动脚本（推荐）**
```bash
python start_simple.py
```

**方法三：使用PowerShell脚本（Windows）**
```powershell
.\start_server.ps1
```

**方法四：使用批处理文件（Windows）**
```cmd
start_server.bat
```

**方法五：直接使用uvicorn**
```bash
uvicorn backend.main:app --host 127.0.0.1 --port 8001
```

7. 访问应用

打开浏览器访问 [http://127.0.0.1:8001](http://127.0.0.1:8001)

## 🏗️ 技术架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   文档处理      │
│                 │    │                 │    │                 │
│ • 三栏编辑器    │◄──►│ • FastAPI       │◄──►│ • Pandoc        │
│ • 实时预览      │    │ • 文件上传      │    │ • Word模板      │
│ • 配置面板      │    │ • 限流控制      │    │ • 封面生成      │
│ • 目录导航      │    │ • 错误处理      │    │ • 后处理器      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心技术栈
- **后端框架**: FastAPI 0.116.1 - 高性能异步 Web 框架
- **文档转换**: Pandoc 3.2+ - 强大的文档格式转换工具
- **Word 处理**: python-docx 1.2.0 - Word 文档操作库
- **前端技术**: 原生 JavaScript + HTML5 + CSS3
- **模板引擎**: Jinja2 3.1.4 - 服务端模板渲染

## 📁 项目结构

```
md2word.com/
├── backend/                    # 后端核心模块
│   ├── __init__.py
│   ├── config.py              # 应用配置和设置
│   ├── main.py                # FastAPI 主应用
│   ├── routes.py              # API 路由定义
│   ├── utils.py               # 核心工具函数
│   ├── cover_generator.py     # 封面图生成器
│   ├── word_template_generator.py  # Word 模板生成器
│   ├── word_post_processor.py # Word 文档后处理器
│   └── template_generator.py  # 模板样式生成器
├── static/                    # 前端静态资源
│   ├── css/                   # 样式文件
│   ├── js/
│   │   └── editor.js          # 编辑器核心逻辑
│   └── img/                   # 图片资源
├── templates/                 # HTML 模板
│   └── index.html             # 主页面模板
├── tests/                     # 测试代码
│   ├── test_api.py            # API 测试
│   ├── test_utils.py          # 工具函数测试
│   └── test_performance.py    # 性能测试
├── workspace/                 # 工作目录
│   └── uploads/               # 临时文件存储
├── docs/                      # 项目文档
├── generated_samples/         # 生成的样本文件
├── requirements.txt           # Python 依赖
└── run.py                     # 应用启动脚本
```

## ⚙️ 核心技术实现

### 1. Markdown 转换系统

#### 标准转换流程
使用 Pandoc 进行高质量的 Markdown 到 Word 转换：

```python
class WordTemplateGenerator:
    def generate_word_template(self, config, filename=None):
        # 创建 Word 文档
        doc = Document()

        # 设置文档基础属性
        self._set_document_properties(doc)

        # 应用字体设置
        self._apply_font_settings(doc, chinese_font, english_font, font_size)

        # 应用模板特定样式
        style_applier = self.template_styles.get(template_type)
        style_applier(doc, font_size)

        # 生成并插入封面图
        if filename:
            cover_generator = CoverGenerator()
            cover_path = cover_generator.generate_cover(config, filename)
            self._insert_cover_image(doc, cover_path)

        return template_path
```

### 2. 文档转换流程

#### 核心转换函数
```python
async def convert_markdown_to_docx(input_path, output_path, reference_path, user_config):
    # 1. 生成 Word 模板和封面图
    template_path, cover_path = generate_font_template(user_config, filename, return_cover_path=True)

    # 2. 构建 Pandoc 转换命令
    cmd = ["pandoc", str(input_path), "-t", "docx", "-o", str(output_path)]

    # 3. 添加目录生成参数
    if user_config.get('generateToc', False):
        cmd.extend(["--toc", f"--toc-depth={toc_depth}"])

    # 4. 使用生成的模板
    cmd.extend(["--reference-doc", str(template_path)])

    # 5. 执行 Pandoc 转换
    result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

    # 6. 后处理：修复字体和添加封面
    if result.returncode == 0:
        post_processor = WordPostProcessor()
        post_processor.process_document(output_path, user_config)

        # 添加封面图到最终文档
        add_cover_to_document(output_path, cover_path)

    return success
```

### 4. Word 文档后处理器

#### WordPostProcessor 类
对 Pandoc 生成的文档进行精细化处理：

```python
class WordPostProcessor:
    def process_document(self, doc_path, config):
        doc = Document(str(doc_path))

        # 修复字体设置
        self._fix_font_settings(doc, config)

        # 添加目录说明
        if config.get('generateToc', False):
            self._add_toc_instructions(doc, config)

        # 应用标题样式
        self._apply_heading_styles(doc, config)

        doc.save(str(doc_path))
```

**后处理功能**：
- **字体修复**: 确保中英文字体正确应用到所有文本
- **目录处理**: 添加 Word 原生目录字段和更新说明
- **样式优化**: 调整标题层级、段落间距等格式

## 🔧 配置

配置选项位于 `backend/config.py` 文件中，可以根据需要修改：

```python
class Settings:
    # 基础配置
    APP_NAME: str = "md2word.com"
    APP_VERSION: str = "1.0.0"

    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True

    # 文件配置
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    FILE_RETENTION_HOURS: int = 2
    ALLOWED_EXTENSIONS: List[str] = [".md", ".markdown", ".txt"]

    # 限流配置
    RATE_LIMIT_PER_DAY: int = 5

    # Pandoc配置
    PANDOC_TIMEOUT: int = 30  # 秒
```

## 🔄 数据流程

### 完整转换流程
```
用户上传 Markdown 文件
         ↓
    文件验证和限流检查
         ↓
    解析用户配置参数
         ↓
    Pandoc 执行格式转换
         ↓
    Word 文档后处理
         ↓
    返回处理后的 Word 文档
         ↓
    自动清理临时文件
```

### API 接口设计

#### POST /convert
主要转换接口，支持以下参数：

```javascript
// 请求参数
{
  file: File,                    // Markdown 文件
  reference: File (可选),        // 参考模板文件
  format: "docx" | "pdf",        // 输出格式：Word 或 PDF
  config: {                      // 配置参数
    chineseFont: "Microsoft YaHei",
    englishFont: "Times New Roman",
    fontSize: 12,
    template: "default",         // default | business | academic
    generateToc: true,
    tocDepth: 3,
    tocFontSize: 13
  }
}

// 响应
{
  filename: "document.docx" | "document.pdf",
  content: Binary Document (Word or PDF)
}
```

#### GET /health
健康检查接口：

```javascript
// 响应
{
  status: "healthy",
  timestamp: "2025-07-16T10:30:00",
  version: "1.0.0"
}
```

## 🧪 测试

### 快速功能测试

**测试Markdown转换功能**
```bash
python test_markdown_conversion.py
```

### 运行测试套件
```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_api.py
pytest tests/test_utils.py
pytest tests/test_performance.py

# 运行测试并生成覆盖率报告
pytest --cov=backend --cov-report=html
```

### 测试覆盖范围
- **API 测试**: 文件上传、转换接口、错误处理
- **工具函数测试**: 文件验证、格式转换
- **性能测试**: 转换速度、内存使用、并发处理
- **集成测试**: 完整转换流程、多种配置组合

### 手动测试脚本
项目包含多个测试脚本用于验证功能：

```bash
# 测试完整转换流程
python test_conversion.py

# 测试 Web 界面转换
python test_web_conversion.py

# 生成样本文件
python generate_samples.py
```

## 🌐 API 文档

启动应用后，访问以下地址查看详细文档：
- **Swagger UI**: [http://localhost:8000/docs](http://localhost:8000/docs)
- **ReDoc**: [http://localhost:8000/redoc](http://localhost:8000/redoc)

## 🚀 性能优化

### 转换性能
- **平均转换时间**: 2-5 秒（取决于文档大小和复杂度）
- **内存使用**: 峰值 30-50MB
- **并发支持**: 支持多用户同时转换
- **文件大小限制**: 10MB（可配置）

### 缓存策略
- **文件清理**: 2 小时后自动清理临时文件

### 错误处理
- **超时保护**: Pandoc 转换 30 秒超时
- **资源清理**: 异常情况下自动清理临时文件
- **详细日志**: 完整的错误追踪和调试信息

## 🔒 安全特性

### 文件安全
- **文件类型验证**: 仅允许 .md、.markdown、.txt 文件
- **文件大小限制**: 防止大文件攻击
- **路径安全**: 防止目录遍历攻击
- **自动清理**: 定期清理临时文件

### 访问控制
- **IP 限流**: 每个 IP 每天限制 5 次转换
- **CORS 配置**: 跨域请求安全控制
- **输入验证**: 严格的参数验证和清理

## � 故障排除

### 常见问题

**1. 服务器启动失败**
- 确保虚拟环境已激活：`venv\Scripts\activate`
- 检查端口是否被占用：`netstat -ano | findstr :8001`
- 尝试使用简化启动脚本：`python start_simple.py`

**2. Pandoc 未找到**
- Windows: 从 [Pandoc官网](https://pandoc.org/installing.html) 下载安装
- 确保 Pandoc 已添加到系统 PATH
- 验证安装：`pandoc --version`

**3. 转换失败**
- 检查 Markdown 文件格式是否正确
- 确保文件大小不超过 10MB
- 查看控制台错误信息

**4. 字体显示问题**
- 确保系统已安装所选字体
- 尝试使用系统默认字体
- 检查字体名称是否正确
- 字体设置功能已修复（v1.0.1），现在可以正常工作

### 测试功能
```bash
# 测试转换功能
python test_markdown_conversion.py
```

## �📊 项目统计

### 代码统计
- **总代码行数**: ~3,000 行
- **Python 代码**: ~2,200 行
- **JavaScript 代码**: ~600 行
- **HTML/CSS**: ~200 行

### 功能模块
- **核心模块**: 8 个主要 Python 模块
- **测试文件**: 6 个测试脚本
- **配置文件**: 完整的配置管理
- **文档**: 详细的开发和使用文档

### 依赖管理
- **Python 依赖**: 12 个核心依赖包
- **外部工具**: Pandoc（必需）
- **开发依赖**: pytest、black、flake8 等

## 📝 开发计划

### 已完成功能 ✅
- [x] 现代化三栏编辑器界面
- [x] 实时 Markdown 预览
- [x] 智能目录导航系统
- [x] 标准 Markdown 到 Word 转换
- [x] 中英文字体分别设置
- [x] Word 原生目录生成
- [x] 完整的文档后处理
- [x] 文件上传和转换 API
- [x] 限流和安全控制

### 未来扩展计划 🔮
- [ ] 更多专业模板（简历、报告等）
- [ ] 用户自定义封面图上传
- [ ] 批量文件转换
- [ ] 在线协作编辑
- [ ] 云端文件存储
- [ ] 移动端适配

详见 [开发计划](开发计划-更新版.md) 和 [开发完成总结](docs/开发完成总结.md) 文档。

## 🤝 贡献

欢迎贡献代码、报告问题或提出改进建议！

### 贡献方式
1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 开发环境
- Python 3.11+
- Node.js 16+ (用于前端工具)
- Pandoc 3.2+
- Git

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

---

## 🎉 项目成就

**md2word.com** 是一个功能完整、技术先进的现代化 Markdown 编辑器和转换工具。从简单的文件转换工具发展为专业级的文档处理平台，实现了质的飞跃。

### 技术亮点
- 🏗️ **现代化架构**: FastAPI + 模块化设计
- 🎨 **专业模板**: 三种内置模板 + 动态封面生成
- ⚡ **高性能**: 2-5秒完成转换，支持并发处理
- 🔒 **安全可靠**: 完善的安全控制和错误处理
- 📱 **用户友好**: 直观的三栏界面，实时预览

### 访问信息
- **本地访问**: http://127.0.0.1:8001
- **代码仓库**: https://gitee.com/bin1874/md2word.git
- **开发完成**: 2025-07-16 🎊
- **最新更新**: 2025-07-31 ✅ (字体功能修复)
