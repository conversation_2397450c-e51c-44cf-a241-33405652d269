# md2word.com 编辑器功能说明

## 🎉 重大功能升级

**版本**: v2.0.0  
**发布日期**: 2025-07-14  
**升级类型**: 主要功能升级

从简单的文件上传工具升级为功能完整的在线Markdown编辑器！

## 🆕 新功能特性

### 1. 三栏式编辑器布局
- **左侧编辑器**: 支持输入、粘贴、编辑Markdown内容
- **中间目录**: 自动提取文档标题，支持快速跳转
- **右侧预览**: 实时显示转换效果，支持代码高亮

### 2. 实时编辑体验
- ✅ 输入即时预览，无需手动刷新
- ✅ 自动语法高亮，支持多种编程语言
- ✅ 完整的Markdown格式支持
- ✅ 表格、列表、引用块实时渲染

### 3. 智能目录导航
- ✅ 自动提取H1-H6标题
- ✅ 层级缩进显示
- ✅ 点击跳转到对应位置
- ✅ 当前位置高亮显示

### 4. 多种内容输入方式
- ✅ 直接在编辑器中输入
- ✅ 粘贴剪贴板内容
- ✅ 上传.md/.markdown/.txt文件
- ✅ 拖拽文件到编辑器

### 5. 增强的文件操作
- ✅ 一键清空编辑器
- ✅ 文件上传对话框
- ✅ 支持Word模板上传
- ✅ 快速导出Word文档

## 🎨 界面设计

### 现代化设计风格
- 简洁的三栏布局
- 渐变色工具栏
- 响应式设计，支持移动端
- 直观的图标和按钮

### 用户体验优化
- 流畅的动画效果
- 清晰的视觉层次
- 友好的错误提示
- 多语言支持（中英文）

## 🔧 技术实现

### 前端技术
- **Markdown解析**: marked.js 9.1.6
- **代码高亮**: highlight.js 11.9.0
- **界面框架**: 原生HTML5 + CSS3 + JavaScript
- **图标库**: Font Awesome 6.0.0

### 核心功能
```javascript
// 实时预览更新
function updatePreview() {
    const html = marked.parse(content);
    previewContent.innerHTML = html;
}

// 目录自动生成
function updateTableOfContents() {
    const headingRegex = /^(#{1,6})\s+(.+)$/gm;
    // 提取标题并生成目录
}

// 智能跳转
function scrollToHeading(index) {
    heading.scrollIntoView({ behavior: 'smooth' });
}
```

## 📱 使用指南

### 基本操作
1. **开始编辑**: 在左侧编辑器中输入Markdown内容
2. **查看预览**: 右侧实时显示渲染效果
3. **导航跳转**: 点击中间目录项快速跳转
4. **导出文档**: 点击工具栏"下载Word"按钮

### 高级功能
1. **上传文件**: 工具栏 → 上传文件 → 选择.md文件
2. **使用模板**: 上传时勾选"使用Word模板"
3. **语言切换**: 工具栏右侧地球图标
4. **拖拽上传**: 直接拖拽文件到编辑器

### 快捷操作
- **Ctrl+A**: 全选编辑器内容
- **Ctrl+C/V**: 复制粘贴
- **拖拽文件**: 快速加载文件内容

## 🎯 支持的Markdown语法

### 基础语法
- 标题 (H1-H6)
- 粗体、斜体、删除线
- 行内代码和代码块
- 链接和图片
- 列表（有序、无序、嵌套）

### 扩展语法
- 表格
- 引用块
- 任务列表
- 代码语法高亮
- 数学公式（部分支持）

### 代码高亮支持
支持100+编程语言，包括：
- JavaScript, Python, Java, C++
- HTML, CSS, SQL, JSON
- Bash, PowerShell, YAML
- 等等...

## 🔄 版本对比

| 功能 | v1.0.0 | v2.0.0 |
|------|--------|--------|
| 文件上传 | ✅ | ✅ |
| 实时编辑 | ❌ | ✅ |
| 实时预览 | ❌ | ✅ |
| 目录导航 | ❌ | ✅ |
| 代码高亮 | ❌ | ✅ |
| 拖拽上传 | ❌ | ✅ |
| 多语言界面 | ✅ | ✅ |
| Word导出 | ✅ | ✅ |

## 🚀 性能表现

- **启动速度**: < 1秒
- **实时预览**: < 100ms 延迟
- **文件加载**: 支持10MB大文件
- **内存占用**: 优化的DOM操作
- **兼容性**: 支持现代浏览器

## 🔮 未来计划

### 短期计划
- [ ] 全屏编辑模式
- [ ] 更多主题选择
- [ ] 导出PDF功能
- [ ] 文档历史记录

### 长期计划
- [ ] 协同编辑功能
- [ ] 云端同步
- [ ] 插件系统
- [ ] 移动端App

## 📞 反馈与支持

如果您在使用过程中遇到问题或有改进建议，欢迎：
- 在GitHub提交Issue
- 发送邮件反馈
- 参与社区讨论

---

**md2word.com** - 让Markdown编辑更简单，让文档转换更高效！
