#!/bin/bash

# md2word.com 生产环境部署脚本
# 前提条件：项目已克隆到 /srv/md2word
# 适用于已有Django网站的Ubuntu服务器
# 使用域名：md2word.com, www.md2word.com
# 部署目录：/srv/md2word
# 应用端口：8003（避免冲突）

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查项目目录是否存在
check_project_directory() {
    if [ ! -d "/srv/md2word" ]; then
        log_error "项目目录 /srv/md2word 不存在"
        log_error "请先执行: cd /srv && git clone https://gitee.com/bin1874/md2word.git"
        exit 1
    fi

    if [ ! -f "/srv/md2word/requirements.txt" ]; then
        log_error "项目文件不完整，请检查克隆是否成功"
        exit 1
    fi

    log_success "项目目录检查通过"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if netstat -tlnp | grep ":$port " > /dev/null; then
        log_error "端口 $port 已被占用，请检查配置"
        netstat -tlnp | grep ":$port "
        exit 1
    fi
}

# 安装系统依赖
install_dependencies() {
    log_info "更新系统包..."
    apt update && apt upgrade -y

    log_info "安装系统依赖..."
    apt install -y python3 python3-pip python3-venv git nginx certbot python3-certbot-nginx pandoc

    log_info "安装Microsoft字体支持..."
    apt install -y ttf-mscorefonts-installer || log_warning "字体安装失败，可能影响PDF质量"

    log_success "系统依赖安装完成"
}

# 配置应用环境
setup_application() {
    log_info "开始配置md2word应用..."

    # 检查端口
    check_port 8003

    # 进入项目目录
    cd /srv/md2word

    # 更新代码到最新版本
    log_info "更新项目代码到最新版本..."
    git pull origin master

    # 设置目录权限
    log_info "设置目录权限..."
    chown -R www-data:www-data /srv/md2word

    # 创建必要目录
    log_info "创建必要的目录..."
    mkdir -p /srv/md2word/uploads /srv/md2word/workspace /var/log/md2word
    chown -R www-data:www-data /srv/md2word/uploads /srv/md2word/workspace /var/log/md2word
    chmod -R 775 /srv/md2word/uploads /srv/md2word/workspace

    # 创建Python虚拟环境
    log_info "创建Python虚拟环境..."
    if [ ! -d "venv" ]; then
        sudo -u www-data python3 -m venv venv
        log_success "虚拟环境创建完成"
    else
        log_info "虚拟环境已存在，跳过创建"
    fi

    # 安装Python依赖
    log_info "安装Python依赖..."
    sudo -u www-data bash -c "source venv/bin/activate && pip install --upgrade pip"
    sudo -u www-data bash -c "source venv/bin/activate && pip install -r requirements.txt"

    log_success "应用环境配置完成"
}

# 创建配置文件
create_config() {
    log_info "创建生产环境配置..."

    cat > /srv/md2word/.env.production << EOF
# md2word.com 生产环境配置
HOST=127.0.0.1
PORT=8003
DEBUG=False
ALLOWED_HOSTS=md2word.com,www.md2word.com
MAX_FILE_SIZE=10485760
FILE_RETENTION_HOURS=2
RATE_LIMIT_PER_DAY=100
LOG_LEVEL=INFO
LOG_FILE=/var/log/md2word/app.log
EOF

    chown www-data:www-data /srv/md2word/.env.production
    log_success "配置文件创建完成"
}

# 配置Systemd服务
setup_systemd() {
    log_info "配置Systemd服务..."

    cat > /etc/systemd/system/md2word.service << EOF
[Unit]
Description=md2word.com FastAPI application
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/srv/md2word
Environment=PATH=/srv/md2word/venv/bin
Environment=PYTHONPATH=/srv/md2word
ExecStart=/srv/md2word/venv/bin/python -m uvicorn backend.main:app --host 127.0.0.1 --port 8003 --workers 2
Restart=always
RestartSec=5

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/srv/md2word/uploads /srv/md2word/workspace /var/log/md2word

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd配置
    systemctl daemon-reload
    systemctl enable md2word.service

    log_success "Systemd服务配置完成"
}

# 配置Nginx
setup_nginx() {
    log_info "配置Nginx..."

    # 检查是否已存在配置
    if [ -f "/etc/nginx/sites-available/md2word.com" ]; then
        log_warning "Nginx配置已存在，备份原配置..."
        cp /etc/nginx/sites-available/md2word.com /etc/nginx/sites-available/md2word.com.backup.$(date +%Y%m%d_%H%M%S)
    fi

    cat > /etc/nginx/sites-available/md2word.com << 'EOF'
# md2word.com 网站配置
server {
    listen 80;
    server_name md2word.com www.md2word.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name md2word.com www.md2word.com;
    
    # SSL配置（Let's Encrypt证书）
    ssl_certificate /etc/letsencrypt/live/md2word.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/md2word.com/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 文件上传大小限制
    client_max_body_size 10M;
    
    # 静态文件服务
    location /static/ {
        alias /srv/md2word/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # 主应用代理
    location / {
        proxy_pass http://127.0.0.1:8003;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8003/health;
        access_log off;
    }
}
EOF

    # 启用站点
    ln -sf /etc/nginx/sites-available/md2word.com /etc/nginx/sites-enabled/

    # 测试Nginx配置
    if nginx -t; then
        log_success "Nginx配置测试通过"
    else
        log_error "Nginx配置测试失败"
        exit 1
    fi
}

# 启动服务
start_services() {
    log_info "启动md2word服务..."
    systemctl start md2word.service

    # 检查服务状态
    if systemctl is-active --quiet md2word.service; then
        log_success "md2word服务启动成功"
    else
        log_error "md2word服务启动失败"
        systemctl status md2word.service
        exit 1
    fi

    log_info "重新加载Nginx..."
    systemctl reload nginx

    log_success "所有服务启动完成"
}

# 配置SSL证书
setup_ssl() {
    log_info "配置SSL证书..."
    
    # 检查域名解析
    log_warning "请确保域名 md2word.com 和 www.md2word.com 已正确解析到此服务器"
    read -p "域名是否已正确解析？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 申请SSL证书
        certbot --nginx -d md2word.com -d www.md2word.com --non-interactive --agree-tos --email <EMAIL>
        
        # 设置自动续期
        echo "0 2 * * * /usr/bin/certbot renew --quiet --nginx" | crontab -
        
        log_success "SSL证书配置完成"
    else
        log_warning "跳过SSL证书配置，请稍后手动配置"
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."

    # 检查服务状态
    if systemctl is-active --quiet md2word.service; then
        log_success "✓ md2word服务运行正常"
    else
        log_error "✗ md2word服务未运行"
    fi

    # 检查端口监听
    if netstat -tlnp | grep ":8003 " > /dev/null; then
        log_success "✓ 端口8003监听正常"
    else
        log_error "✗ 端口8003未监听"
    fi

    # 检查Nginx配置
    if nginx -t > /dev/null 2>&1; then
        log_success "✓ Nginx配置正确"
    else
        log_error "✗ Nginx配置有误"
    fi

    log_info "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    echo
    echo "=========================================="
    echo "         md2word.com 部署完成"
    echo "=========================================="
    echo
    echo "网站地址："
    echo "  - https://md2word.com"
    echo "  - https://www.md2word.com"
    echo
    echo "服务管理命令："
    echo "  - 查看状态: systemctl status md2word.service"
    echo "  - 重启服务: systemctl restart md2word.service"
    echo "  - 查看日志: journalctl -u md2word.service -f"
    echo
    echo "文件位置："
    echo "  - 应用目录: /srv/md2word"
    echo "  - 日志目录: /var/log/md2word"
    echo "  - 配置文件: /srv/md2word/.env.production"
    echo
    echo "注意事项："
    echo "  - 应用运行在端口8003"
    echo "  - 通过Nginx代理提供服务"
    echo "  - 文件会在2小时后自动清理"
    echo
    echo "=========================================="
}

# 显示前置条件检查
show_prerequisites() {
    echo
    echo "=========================================="
    echo "      md2word.com 部署脚本"
    echo "=========================================="
    echo
    echo "前置条件检查："
    echo "  ✓ 项目已克隆到 /srv/md2word"
    echo "  ✓ 当前用户为 root"
    echo "  ✓ 服务器为 Ubuntu 系统"
    echo
    echo "即将执行的操作："
    echo "  1. 安装系统依赖"
    echo "  2. 配置应用环境"
    echo "  3. 创建配置文件"
    echo "  4. 设置 Systemd 服务"
    echo "  5. 配置 Nginx"
    echo "  6. 启动服务"
    echo "  7. 配置 SSL 证书"
    echo "  8. 验证部署"
    echo
    echo "注意：此脚本会与现有 Django 网站共存"
    echo "使用端口：8003（避免冲突）"
    echo
    read -p "是否继续部署？(y/N): " -n 1 -r
    echo

    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
}

# 主函数
main() {
    show_prerequisites

    log_info "开始部署md2word.com到生产环境..."

    check_root
    check_project_directory
    install_dependencies
    setup_application
    create_config
    setup_systemd
    setup_nginx
    start_services
    setup_ssl
    verify_deployment
    show_deployment_info

    log_success "部署完成！"
}

# 执行主函数
main "$@"
