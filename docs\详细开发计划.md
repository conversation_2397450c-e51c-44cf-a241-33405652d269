# md2word.com 详细开发计划

## 📋 当前状态分析

### ✅ 已完成功能
- 现代化三栏编辑器界面
- 可拖拽调整面板大小
- 实时Markdown预览和目录导航
- 完整的前端配置面板
- 基础Markdown到Word转换
- Git仓库初始化和代码管理

### 🔄 待完善功能
1. **Word文档字体设置后端实现** (优先级：高)
2. **Word文档目录生成功能** (优先级：高)
3. **内置Word模板系统完善** (优先级：中)

## 🎯 开发计划详情

### 阶段1：Word文档字体设置后端实现 (预计2-3小时)

#### 1.1 技术方案
- **目标**：让前端字体配置真正生效到Word输出
- **技术栈**：Python + Pandoc + 自定义CSS/模板
- **实现方式**：动态生成Pandoc参考文档

#### 1.2 具体任务
1. **扩展后端API** (30分钟)
   - 修改`/convert`接口，接收字体配置参数
   - 解析前端传递的字体设置JSON

2. **实现字体模板生成** (60分钟)
   - 创建`generate_font_template()`函数
   - 根据用户配置生成CSS样式
   - 使用Pandoc将CSS转换为Word模板

3. **集成Pandoc转换** (45分钟)
   - 修改`convert_markdown_to_docx()`函数
   - 支持动态生成的字体模板
   - 确保中英文字体正确应用

4. **测试验证** (30分钟)
   - 测试不同字体组合
   - 验证中英文字体分离效果
   - 确保字体大小正确应用

#### 1.3 技术细节
```python
# 字体模板生成示例
def generate_font_template(config):
    css_content = f"""
    body {{
        font-family: '{config["englishFont"]}', '{config["chineseFont"]}';
        font-size: {config["fontSize"]}pt;
    }}
    h1, h2, h3, h4, h5, h6 {{
        font-family: '{config["chineseFont"]}', '{config["englishFont"]}';
    }}
    """
    # 转换为Word模板
```

### 阶段2：Word文档目录生成功能 (预计2-3小时)

#### 2.1 技术方案
- **目标**：导出的Word文档自动包含可导航目录
- **技术栈**：Pandoc TOC功能 + 自定义Lua过滤器
- **实现方式**：Pandoc `--toc` 参数 + 目录样式定制

#### 2.2 具体任务
1. **前端目录配置传递** (30分钟)
   - 确保目录配置正确传递到后端
   - 包括：是否生成目录、目录深度

2. **后端目录生成逻辑** (90分钟)
   - 修改转换函数，支持`--toc`参数
   - 实现目录深度控制
   - 创建目录样式模板

3. **目录样式定制** (60分钟)
   - 设计美观的目录样式
   - 支持不同模板的目录风格
   - 确保目录链接正常工作

4. **测试验证** (30分钟)
   - 测试不同目录深度
   - 验证目录链接跳转
   - 确保目录样式美观

#### 2.3 技术细节
```python
# Pandoc TOC实现
def convert_with_toc(input_path, output_path, config):
    cmd = ["pandoc", str(input_path)]
    
    if config.get("generateToc", False):
        cmd.extend(["--toc", f"--toc-depth={config.get('tocDepth', 3)}"])
    
    cmd.extend(["-t", "docx", "-o", str(output_path)])
```

### 阶段3：内置Word模板系统完善 (预计3-4小时)

#### 3.1 技术方案
- **目标**：提供多种内置Word模板样式
- **技术栈**：预制Word模板 + 动态样式生成
- **实现方式**：模板文件 + CSS样式注入

#### 3.2 具体任务
1. **设计模板样式** (90分钟)
   - **默认模板**：简洁清爽，适合日常使用
   - **商务模板**：专业正式，适合商务文档
   - **学术模板**：严谨规范，适合学术论文

2. **实现模板生成器** (120分钟)
   - 创建`TemplateGenerator`类
   - 实现不同模板的样式定义
   - 支持模板与字体设置结合

3. **集成模板系统** (60分钟)
   - 修改转换流程，支持模板选择
   - 确保模板与其他配置兼容
   - 优化模板加载性能

4. **测试验证** (30分钟)
   - 测试所有模板样式
   - 验证模板与字体的结合效果
   - 确保模板切换正常

#### 3.3 模板设计规范
```css
/* 默认模板 */
.default-template {
    font-family: 'Times New Roman', '宋体';
    line-height: 1.5;
    margin: 2.54cm;
}

/* 商务模板 */
.business-template {
    font-family: 'Arial', '微软雅黑';
    line-height: 1.6;
    color: #333;
}

/* 学术模板 */
.academic-template {
    font-family: 'Times New Roman', '宋体';
    line-height: 2.0;
    text-align: justify;
}
```

## 🚀 开发时间安排

### 第1天 (4-6小时)
- **上午 (2-3小时)**：阶段1 - Word字体设置后端实现
- **下午 (2-3小时)**：阶段2 - Word目录生成功能

### 第2天 (3-4小时)
- **全天**：阶段3 - 内置模板系统完善

### 总计：7-10小时

## 🔧 技术实现路线

### 1. 后端架构优化
```python
# 新增配置处理模块
class ConfigProcessor:
    def process_font_config(self, config): pass
    def process_toc_config(self, config): pass
    def process_template_config(self, config): pass

# 新增模板生成器
class TemplateGenerator:
    def generate_font_template(self, config): pass
    def generate_style_template(self, template_type): pass
    def combine_templates(self, *templates): pass
```

### 2. 转换流程优化
```python
async def enhanced_convert_markdown_to_docx(
    input_path, output_path, user_config
):
    # 1. 处理字体配置
    font_template = generate_font_template(user_config)
    
    # 2. 处理模板配置
    style_template = generate_style_template(user_config)
    
    # 3. 合并模板
    final_template = combine_templates(font_template, style_template)
    
    # 4. 构建Pandoc命令
    cmd = build_pandoc_command(input_path, output_path, user_config, final_template)
    
    # 5. 执行转换
    return await execute_conversion(cmd)
```

## 📊 成功标准

### 功能完成标准
1. **字体设置**：用户选择的中英文字体在Word文档中正确显示
2. **目录生成**：Word文档包含可导航的目录，支持用户配置的深度
3. **模板系统**：三种模板样式明显区别，效果美观专业

### 质量标准
1. **性能**：转换时间不超过原来的2倍
2. **兼容性**：支持所有现有功能，无回归问题
3. **用户体验**：配置简单直观，效果立即可见

### 测试标准
1. **单元测试**：所有新功能有对应测试用例
2. **集成测试**：与现有功能完美集成
3. **用户测试**：实际使用场景验证

## 🎯 开发优先级

### P0 (必须完成)
- Word字体设置后端实现
- Word目录生成功能

### P1 (重要)
- 内置模板系统基础功能

### P2 (可选)
- 模板样式优化
- 高级配置选项

## 📝 开发注意事项

### 1. 代码质量
- 遵循现有代码风格
- 添加详细注释和文档
- 确保错误处理完善

### 2. 向后兼容
- 不破坏现有功能
- 配置参数向后兼容
- API接口保持稳定

### 3. 性能考虑
- 模板生成缓存
- 避免重复计算
- 优化文件I/O操作

---

**开发计划制定完成！** 接下来将按照此计划逐步实现所有功能。
