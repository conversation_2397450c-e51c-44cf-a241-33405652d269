# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
workspace/uploads/*
!workspace/uploads/.gitkeep
workspaceuploads/
*.log
.env
.env.local
.env.production

# Test files and outputs
test-files/
*.docx
test_*.md
test_*.txt

# Test coverage
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Temporary files
*.tmp
*.temp
test_output.*

# Configuration files with sensitive data
1111claude/

# Development files
# main.py  # 注释掉，因为backend/main.py是必需的核心文件
