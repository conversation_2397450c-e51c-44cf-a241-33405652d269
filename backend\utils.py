"""
工具函数 - md2word.com
"""

import subprocess
import uuid
import asyncio
import platform
from pathlib import Path
from typing import Optional
from datetime import datetime, timedelta
from collections import defaultdict

from fastapi import Request, UploadFile
from .config import settings
from .word_post_processor import WordPostProcessor

# PDF转换相关导入
try:
    from docx2pdf import convert as docx2pdf_convert
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("Warning: docx2pdf not available. PDF conversion will be disabled.")

def convert_docx_to_pdf_with_libreoffice(docx_path: str, pdf_path: str) -> bool:
    """
    使用LibreOffice将DOCX文件转换为PDF

    Args:
        docx_path: 输入的DOCX文件路径
        pdf_path: 输出的PDF文件路径

    Returns:
        bool: 转换是否成功
    """
    try:
        import os
        from pathlib import Path

        docx_file = Path(docx_path)
        pdf_file = Path(pdf_path)

        if not docx_file.exists():
            print(f"❌ DOCX文件不存在: {docx_path}")
            return False

        # 获取输出目录
        output_dir = pdf_file.parent

        # LibreOffice命令
        cmd = [
            "libreoffice",
            "--headless",
            "--convert-to", "pdf",
            "--outdir", str(output_dir),
            str(docx_file)
        ]

        print(f"🔄 LibreOffice转换命令: {' '.join(cmd)}")

        # 执行转换
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60  # 60秒超时
        )

        if result.returncode == 0:
            # LibreOffice会生成与输入文件同名的PDF文件
            generated_pdf = output_dir / f"{docx_file.stem}.pdf"

            if generated_pdf.exists():
                # 如果目标文件名不同，重命名
                if generated_pdf != pdf_file:
                    generated_pdf.rename(pdf_file)

                print(f"✅ LibreOffice转换成功: {pdf_file}")
                return True
            else:
                print(f"❌ LibreOffice转换失败: PDF文件未生成")
                print(f"   标准输出: {result.stdout}")
                print(f"   标准错误: {result.stderr}")
                return False
        else:
            print(f"❌ LibreOffice转换失败: 返回码 {result.returncode}")
            print(f"   标准输出: {result.stdout}")
            print(f"   标准错误: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ LibreOffice转换超时")
        return False
    except Exception as e:
        print(f"❌ LibreOffice转换异常: {e}")
        return False

def convert_docx_to_pdf_smart(docx_path: str, pdf_path: str) -> bool:
    """
    智能PDF转换：根据操作系统选择最佳转换方式

    Windows: 优先使用docx2pdf (需要Microsoft Word)，失败则使用LibreOffice
    Linux/macOS: 使用LibreOffice

    Args:
        docx_path: 输入的DOCX文件路径
        pdf_path: 输出的PDF文件路径

    Returns:
        bool: 转换是否成功
    """
    system = platform.system().lower()
    print(f"🖥️ 检测到操作系统: {system}")

    if system == "windows":
        print("🪟 Windows环境：尝试使用docx2pdf转换")
        try:
            if PDF_AVAILABLE:
                docx2pdf_convert(str(docx_path), str(pdf_path))

                # 验证转换结果
                pdf_file = Path(pdf_path)
                if pdf_file.exists() and pdf_file.stat().st_size > 0:
                    print("✅ docx2pdf转换成功")
                    return True
                else:
                    print("❌ docx2pdf转换失败：文件未生成或为空")

            else:
                print("⚠️ docx2pdf不可用")

        except Exception as e:
            print(f"❌ docx2pdf转换失败: {e}")

        # Windows下docx2pdf失败，尝试LibreOffice
        print("🔄 docx2pdf失败，尝试LibreOffice...")
        return convert_docx_to_pdf_with_libreoffice(docx_path, pdf_path)

    else:
        # Linux/macOS环境，直接使用LibreOffice
        print(f"🐧 {system.title()}环境：使用LibreOffice转换")
        return convert_docx_to_pdf_with_libreoffice(docx_path, pdf_path)

# 简单的内存限流器
rate_limiter = defaultdict(lambda: {"count": 0, "date": datetime.now().date()})

def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    forwarded = request.headers.get("X-Forwarded-For")
    if forwarded:
        return forwarded.split(",")[0].strip()
    return request.client.host

def check_rate_limit(ip: str) -> bool:
    """检查IP是否超过限流"""
    today = datetime.now().date()
    
    # 重置计数器如果是新的一天
    if rate_limiter[ip]["date"] != today:
        rate_limiter[ip] = {"count": 0, "date": today}
    
    return rate_limiter[ip]["count"] < settings.RATE_LIMIT_PER_DAY

def increment_rate_limit(ip: str):
    """增加IP的请求计数"""
    rate_limiter[ip]["count"] += 1

def validate_file(file: UploadFile) -> bool:
    """验证上传的文件"""
    if not file.filename:
        return False
    
    file_ext = Path(file.filename).suffix.lower()
    return file_ext in settings.ALLOWED_EXTENSIONS

def generate_unique_filename(original_filename: str, suffix: str = "") -> str:
    """生成唯一文件名"""
    file_id = str(uuid.uuid4())
    file_ext = Path(original_filename).suffix
    return f"{file_id}{suffix}{file_ext}"

async def convert_markdown_to_docx(
    input_path: Path,
    output_path: Path,
    reference_path: Optional[Path] = None,
    user_config: Optional[dict] = None
) -> bool:
    """使用Pandoc转换Markdown到Word"""
    try:
        # 构建基础命令
        cmd = ["pandoc", str(input_path), "-t", "docx", "-o", str(output_path)]

        # 处理用户配置
        if user_config:
            print(f"Processing user config: {user_config}")



        # 选择参考模板（优先级：用户上传 > 无）
        if reference_path and reference_path.exists():
            cmd.extend(["--reference-doc", str(reference_path)])
            print(f"Using user uploaded reference: {reference_path}")
        else:
            print("No reference template used")

        print(f"Pandoc command: {' '.join(cmd)}")

        # 检查文件状态
        print(f"📁 输入文件: {input_path} (存在: {input_path.exists()}, 大小: {input_path.stat().st_size if input_path.exists() else 'N/A'})")
        if reference_path:
            print(f"📄 参考文件: {reference_path} (存在: {reference_path.exists()}, 大小: {reference_path.stat().st_size if reference_path.exists() else 'N/A'})")
        print(f"📂 输出目录: {output_path.parent} (存在: {output_path.parent.exists()})")

        # 执行转换
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=settings.PANDOC_TIMEOUT,
                cwd=str(Path.cwd()),
                shell=False
            )

            print(f"🔍 Pandoc 执行结果:")
            print(f"   返回码: {result.returncode}")
            print(f"   标准输出: {result.stdout[:300] if result.stdout else '(空)'}")
            print(f"   标准错误: {result.stderr[:300] if result.stderr else '(空)'}")

        except subprocess.TimeoutExpired as e:
            print(f"❌ Pandoc 转换超时 ({settings.PANDOC_TIMEOUT}秒)")
            return False
        except OSError as e:
            print(f"❌ Pandoc 进程启动失败: {e}")
            print(f"   错误代码: {getattr(e, 'winerror', 'N/A')}")
            return False
        except Exception as e:
            print(f"❌ Pandoc 执行异常: {e}")
            import traceback
            print(f"   完整堆栈: {traceback.format_exc()}")
            return False

        if result.returncode == 0:
            print("Pandoc conversion successful")

            # 后处理Word文档
            if user_config:
                post_processor = WordPostProcessor()
                post_success = post_processor.process_document(output_path, user_config)
                if post_success:
                    print("Word post-processing successful")
                else:
                    print("Word post-processing failed, but conversion completed")


            return True
        else:
            print(f"❌ Pandoc 转换失败 (返回码: {result.returncode})")
            print(f"   错误详情: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ 转换过程异常: {e}")
        print(f"   异常类型: {type(e).__name__}")
        import traceback
        print(f"   完整堆栈: {traceback.format_exc()}")
        return False



async def cleanup_old_files():
    """清理超过保留时间的文件"""
    cutoff_time = datetime.now() - timedelta(hours=settings.FILE_RETENTION_HOURS)
    
    for file_path in settings.UPLOAD_DIR.glob("*"):
        if file_path.is_file():
            file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
            if file_time < cutoff_time:
                try:
                    file_path.unlink()
                    print(f"Cleaned up old file: {file_path}")
                except Exception as e:
                    print(f"Error cleaning up {file_path}: {e}")

def check_pandoc_availability() -> bool:
    """检查Pandoc是否可用"""
    try:
        result = subprocess.run(["pandoc", "--version"], capture_output=True)
        return result.returncode == 0
    except Exception:
        return False

async def convert_markdown_to_pdf(
    input_path: Path,
    output_path: Path,
    reference_path: Optional[Path] = None,
    user_config: Optional[dict] = None
) -> bool:
    """
    将Markdown文件转换为PDF（通过Word中间格式）

    转换流程：Markdown → Word → PDF
    这样可以保证PDF和Word的格式完全一致，避免格式丢失和乱码问题

    Args:
        input_path: 输入Markdown文件路径
        output_path: 输出PDF文件路径
        reference_path: 参考模板文件路径（可选）
        user_config: 用户配置（可选）

    Returns:
        bool: 转换是否成功
    """
    if not PDF_AVAILABLE:
        print("❌ PDF转换功能不可用：docx2pdf未安装")
        return False

    temp_docx_path = None
    try:
        print(f"📄 开始PDF转换: {input_path} -> {output_path}")
        print("🔄 采用两步转换：Markdown → Word → PDF")

        # 第一步：生成临时Word文件
        temp_docx_path = output_path.with_suffix('.temp.docx')
        print(f"📝 第一步：转换为临时Word文件 {temp_docx_path}")

        # 调用现有的Word转换函数
        word_success = await convert_markdown_to_docx(
            input_path,
            temp_docx_path,
            reference_path,
            user_config
        )

        if not word_success:
            print("❌ Word转换失败，无法继续PDF转换")
            return False

        print("✅ Word转换成功")

        # 第二步：将Word文件转换为PDF
        print(f"📄 第二步：转换Word为PDF {output_path}")

        # 使用智能PDF转换（根据操作系统自动选择）
        success = convert_docx_to_pdf_smart(str(temp_docx_path), str(output_path))

        if not success:
            print("❌ PDF转换失败")
            return False

        print("✅ PDF转换成功")

        # 验证PDF文件是否生成
        if not output_path.exists():
            print("❌ PDF文件未生成")
            return False

        pdf_size = output_path.stat().st_size
        print(f"📏 PDF文件大小: {pdf_size} 字节")

        if pdf_size == 0:
            print("❌ PDF文件为空")
            return False

        return True

    except Exception as e:
        print(f"❌ PDF转换异常: {e}")
        import traceback
        print(f"   完整堆栈: {traceback.format_exc()}")
        return False

    finally:
        # 清理临时Word文件
        if temp_docx_path and temp_docx_path.exists():
            try:
                temp_docx_path.unlink()
                print(f"🧹 已清理临时文件: {temp_docx_path}")
            except Exception as e:
                print(f"⚠️  清理临时文件失败: {e}")



