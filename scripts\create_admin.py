#!/usr/bin/env python3
"""
创建管理员账号脚本 - md2word.com
使用方法：
    python scripts/create_admin.py
    python scripts/create_admin.py --username admin --password your_password
"""

import sys
import os
import argparse
import getpass
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.auth import create_admin_user
from backend.database import init_database, test_connection


def main():
    parser = argparse.ArgumentParser(description='创建管理员账号')
    parser.add_argument('--username', '-u', help='管理员用户名')
    parser.add_argument('--password', '-p', help='管理员密码')
    parser.add_argument('--force', '-f', action='store_true', help='强制创建（覆盖已存在的用户）')
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("md2word.com 管理员账号创建工具")
    print("=" * 50)
    
    # 测试数据库连接
    print("正在测试数据库连接...")
    if not test_connection():
        print("❌ 数据库连接失败！请检查数据库配置。")
        print("请确保：")
        print("1. MySQL服务正在运行")
        print("2. 数据库配置正确（DATABASE_URL环境变量）")
        print("3. 数据库用户有足够的权限")
        return 1
    
    print("✅ 数据库连接成功")
    
    # 初始化数据库（如果需要）
    try:
        print("正在初始化数据库...")
        init_database()
        print("✅ 数据库初始化完成")
    except Exception as e:
        print(f"⚠️  数据库初始化警告: {e}")
    
    # 获取用户名
    username = args.username
    if not username:
        username = input("请输入管理员用户名: ").strip()
        if not username:
            print("❌ 用户名不能为空")
            return 1
    
    # 获取密码
    password = args.password
    if not password:
        password = getpass.getpass("请输入管理员密码: ")
        if not password:
            print("❌ 密码不能为空")
            return 1
        
        # 确认密码
        password_confirm = getpass.getpass("请再次输入密码确认: ")
        if password != password_confirm:
            print("❌ 两次输入的密码不一致")
            return 1
    
    # 密码强度检查
    if len(password) < 6:
        print("❌ 密码长度至少需要6个字符")
        return 1
    
    # 创建管理员账号
    print(f"\n正在创建管理员账号: {username}")
    
    try:
        success = create_admin_user(username, password)
        
        if success:
            print("✅ 管理员账号创建成功！")
            print(f"用户名: {username}")
            print("密码: ********")
            print("\n您现在可以使用以下地址登录管理后台：")
            print("http://localhost:8001/admin/login")
            print("或")
            print("https://www.md2word.com/admin/login")
            
            # 保存到环境变量文件（可选）
            env_file = project_root / ".env"
            if not env_file.exists():
                try:
                    with open(env_file, 'w') as f:
                        f.write(f"ADMIN_USERNAME={username}\n")
                        f.write(f"ADMIN_PASSWORD={password}\n")
                        f.write("SECRET_KEY=your-secret-key-change-in-production\n")
                        f.write("DATABASE_URL=mysql+pymysql://md2word:md2word123@localhost:3306/md2word\n")
                    print(f"\n✅ 环境变量已保存到 {env_file}")
                except Exception as e:
                    print(f"⚠️  保存环境变量失败: {e}")
            
            return 0
        else:
            print("❌ 管理员账号创建失败")
            print("可能的原因：")
            print("1. 用户名已存在")
            print("2. 数据库权限不足")
            print("3. 数据库连接问题")
            return 1
            
    except Exception as e:
        print(f"❌ 创建管理员账号时发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
