"""
认证和授权系统 - md2word.com
"""

from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from .config import settings
from .database import get_db, get_db_context
from .models import AdminUser

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Bearer token
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.JWT_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[str]:
    """验证令牌并返回用户名"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return username
    except JWTError:
        return None


def authenticate_admin(username: str, password: str) -> Optional[AdminUser]:
    """验证管理员用户"""
    with get_db_context() as db:
        admin = db.query(AdminUser).filter(AdminUser.username == username).first()
        if not admin:
            return None
        if not verify_password(password, admin.password_hash):
            return None
        if not admin.is_active:
            return None
        
        # 更新最后登录时间
        admin.last_login = datetime.utcnow()
        db.commit()
        
        return admin


def get_current_admin(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> AdminUser:
    """获取当前管理员用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    username = verify_token(credentials.credentials)
    if username is None:
        raise credentials_exception
    
    admin = db.query(AdminUser).filter(AdminUser.username == username).first()
    if admin is None or not admin.is_active:
        raise credentials_exception
    
    return admin


def create_admin_user(username: str, password: str) -> bool:
    """创建管理员用户"""
    try:
        with get_db_context() as db:
            # 检查用户是否已存在
            existing_admin = db.query(AdminUser).filter(AdminUser.username == username).first()
            if existing_admin:
                print(f"管理员用户 {username} 已存在")
                return False
            
            # 创建新管理员
            admin = AdminUser(
                username=username,
                password_hash=get_password_hash(password),
                is_active=True
            )
            db.add(admin)
            db.commit()
            
            print(f"管理员用户 {username} 创建成功")
            return True
    except Exception as e:
        print(f"创建管理员用户失败: {e}")
        return False
