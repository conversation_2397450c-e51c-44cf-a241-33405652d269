#!/bin/bash

# md2word.com 简化部署脚本
# 前提条件：项目已克隆到 /srv/md2word
# 当前状态：root@VM-0-12-ubuntu:/srv# ls -> md2word  topfreeaitools

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查是否为root
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
    
    # 检查项目目录
    if [ ! -d "/srv/md2word" ]; then
        log_error "项目目录 /srv/md2word 不存在"
        exit 1
    fi
    
    # 检查关键文件
    if [ ! -f "/srv/md2word/requirements.txt" ]; then
        log_error "项目文件不完整"
        exit 1
    fi
    
    # 检查端口占用
    if netstat -tlnp | grep ":8003 " > /dev/null; then
        log_error "端口 8003 已被占用"
        netstat -tlnp | grep ":8003 "
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    apt update
    apt install -y python3 python3-pip python3-venv nginx certbot python3-certbot-nginx pandoc
    
    # 安装字体支持
    apt install -y ttf-mscorefonts-installer || log_warning "字体安装失败"
    
    log_success "系统依赖安装完成"
}

# 配置应用
setup_application() {
    log_info "配置应用环境..."
    
    cd /srv/md2word
    
    # 更新代码
    git pull origin master
    
    # 设置权限
    chown -R www-data:www-data /srv/md2word
    
    # 创建目录
    mkdir -p uploads workspace /var/log/md2word
    chown -R www-data:www-data uploads workspace /var/log/md2word
    chmod -R 775 uploads workspace
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        sudo -u www-data python3 -m venv venv
    fi
    
    # 安装依赖
    sudo -u www-data bash -c "source venv/bin/activate && pip install --upgrade pip"
    sudo -u www-data bash -c "source venv/bin/activate && pip install -r requirements.txt"
    
    log_success "应用配置完成"
}

# 创建配置文件
create_config() {
    log_info "创建配置文件..."
    
    cat > /srv/md2word/.env.production << EOF
HOST=127.0.0.1
PORT=8003
DEBUG=False
ALLOWED_HOSTS=md2word.com,www.md2word.com
MAX_FILE_SIZE=10485760
FILE_RETENTION_HOURS=2
RATE_LIMIT_PER_DAY=100
LOG_LEVEL=INFO
LOG_FILE=/var/log/md2word/app.log
EOF
    
    chown www-data:www-data /srv/md2word/.env.production
    log_success "配置文件创建完成"
}

# 配置Systemd服务
setup_systemd() {
    log_info "配置Systemd服务..."
    
    cat > /etc/systemd/system/md2word.service << EOF
[Unit]
Description=md2word.com FastAPI application
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/srv/md2word
Environment=PATH=/srv/md2word/venv/bin
Environment=PYTHONPATH=/srv/md2word
ExecStart=/srv/md2word/venv/bin/python -m uvicorn backend.main:app --host 127.0.0.1 --port 8003 --workers 2
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable md2word.service
    
    log_success "Systemd服务配置完成"
}

# 配置Nginx
setup_nginx() {
    log_info "配置Nginx..."
    
    cat > /etc/nginx/sites-available/md2word.com << 'EOF'
server {
    listen 80;
    server_name md2word.com www.md2word.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name md2word.com www.md2word.com;
    
    # SSL配置（稍后由certbot自动配置）
    ssl_certificate /etc/letsencrypt/live/md2word.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/md2word.com/privkey.pem;
    
    client_max_body_size 10M;
    
    location /static/ {
        alias /srv/md2word/static/;
        expires 1y;
        access_log off;
    }
    
    location / {
        proxy_pass http://127.0.0.1:8003;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
    
    # 启用站点
    ln -sf /etc/nginx/sites-available/md2word.com /etc/nginx/sites-enabled/
    
    # 测试配置
    if nginx -t; then
        log_success "Nginx配置完成"
    else
        log_error "Nginx配置测试失败"
        exit 1
    fi
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动md2word服务
    systemctl start md2word.service
    
    if systemctl is-active --quiet md2word.service; then
        log_success "md2word服务启动成功"
    else
        log_error "md2word服务启动失败"
        systemctl status md2word.service
        exit 1
    fi
    
    # 重新加载Nginx
    systemctl reload nginx
    
    log_success "服务启动完成"
}

# 配置SSL
setup_ssl() {
    log_info "配置SSL证书..."
    
    echo "请确保域名 md2word.com 和 www.md2word.com 已解析到此服务器"
    read -p "域名是否已正确解析？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 申请证书
        certbot --nginx -d md2word.com -d www.md2word.com --non-interactive --agree-tos --email <EMAIL> || {
            log_warning "SSL证书申请失败，请稍后手动配置"
            return
        }
        
        # 设置自动续期
        echo "0 2 * * * /usr/bin/certbot renew --quiet --nginx" | crontab -
        
        log_success "SSL证书配置完成"
    else
        log_warning "跳过SSL配置，请稍后手动执行："
        echo "certbot --nginx -d md2word.com -d www.md2word.com"
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查服务状态
    if systemctl is-active --quiet md2word.service; then
        log_success "✓ md2word服务运行正常"
    else
        log_error "✗ md2word服务未运行"
    fi
    
    # 检查端口
    if netstat -tlnp | grep ":8003 " > /dev/null; then
        log_success "✓ 端口8003监听正常"
    else
        log_error "✗ 端口8003未监听"
    fi
    
    # 检查Nginx
    if nginx -t > /dev/null 2>&1; then
        log_success "✓ Nginx配置正确"
    else
        log_error "✗ Nginx配置有误"
    fi
    
    log_success "部署验证完成"
}

# 显示部署信息
show_info() {
    echo
    echo "=========================================="
    echo "         md2word.com 部署完成"
    echo "=========================================="
    echo
    echo "网站地址: https://md2word.com"
    echo "应用端口: 8003"
    echo "项目目录: /srv/md2word"
    echo "日志目录: /var/log/md2word"
    echo
    echo "管理命令:"
    echo "  systemctl status md2word.service"
    echo "  systemctl restart md2word.service"
    echo "  journalctl -u md2word.service -f"
    echo
    echo "=========================================="
}

# 主函数
main() {
    echo "md2word.com 部署脚本 (从已克隆项目开始)"
    echo "项目目录: /srv/md2word"
    echo
    
    check_prerequisites
    install_dependencies
    setup_application
    create_config
    setup_systemd
    setup_nginx
    start_services
    setup_ssl
    verify_deployment
    show_info
    
    log_success "部署完成！"
}

# 执行主函数
main "$@"
