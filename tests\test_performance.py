"""
性能测试
"""

import pytest
import time
from io import BytesIO
from fastapi.testclient import TestClient

def test_conversion_speed_small_file(client: TestClient):
    """测试小文件转换速度"""
    # 创建一个小的测试文件（约1KB）
    content = """# 性能测试文档

这是一个用于性能测试的小文档。

## 内容

- 列表项1
- 列表项2
- 列表项3

### 代码示例

```python
def hello():
    return "Hello, World!"
```

### 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| A   | B   | C   |
| 1   | 2   | 3   |
""".encode('utf-8')
    
    files = {
        "file": ("performance_test.md", BytesIO(content), "text/markdown")
    }
    
    start_time = time.time()
    response = client.post("/convert", files=files)
    end_time = time.time()
    
    conversion_time = end_time - start_time
    
    assert response.status_code == 200
    assert conversion_time < 2.0  # 应该在2秒内完成
    
    print(f"Small file conversion time: {conversion_time:.2f} seconds")

def test_conversion_speed_medium_file(client: TestClient):
    """测试中等文件转换速度"""
    # 创建一个中等大小的测试文件（约100KB）
    base_content = """# 中等文件性能测试

这是一个用于测试中等大小文件转换性能的文档。

## 重复内容

"""
    
    repeated_content = """
### 章节

这是一个重复的章节内容，用于增加文件大小。

- 项目1
- 项目2
- 项目3

```python
def example_function():
    return "This is an example"
```

| 列A | 列B | 列C |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |

"""
    
    # 重复内容以达到约100KB
    full_content = base_content + (repeated_content * 100)
    content = full_content.encode('utf-8')
    
    files = {
        "file": ("medium_performance_test.md", BytesIO(content), "text/markdown")
    }
    
    start_time = time.time()
    response = client.post("/convert", files=files)
    end_time = time.time()
    
    conversion_time = end_time - start_time
    
    assert response.status_code == 200
    assert conversion_time < 5.0  # 应该在5秒内完成
    
    print(f"Medium file conversion time: {conversion_time:.2f} seconds")
    print(f"File size: {len(content) / 1024:.1f} KB")

@pytest.mark.slow
def test_conversion_speed_large_file(client: TestClient):
    """测试大文件转换速度（标记为慢速测试）"""
    # 创建一个接近限制的大文件（约5MB）
    base_content = """# 大文件性能测试

这是一个用于测试大文件转换性能的文档。

## 大量重复内容

"""
    
    repeated_content = """
### 重复章节

这是一个重复的章节，包含大量内容用于测试性能。

#### 子章节

- 长列表项目1：这是一个很长的列表项目，包含大量文本内容
- 长列表项目2：这是另一个很长的列表项目，包含更多文本内容
- 长列表项目3：这是第三个很长的列表项目，继续添加文本内容

```python
def complex_function():
    # 这是一个复杂的函数示例
    data = {
        'key1': 'value1',
        'key2': 'value2',
        'key3': 'value3'
    }
    
    for key, value in data.items():
        print(f"{key}: {value}")
    
    return data
```

##### 详细表格

| 列1 | 列2 | 列3 | 列4 | 列5 |
|-----|-----|-----|-----|-----|
| 详细数据1 | 详细数据2 | 详细数据3 | 详细数据4 | 详细数据5 |
| 更多数据1 | 更多数据2 | 更多数据3 | 更多数据4 | 更多数据5 |

> 这是一个引用块，包含重要的信息和说明。
> 引用块可以包含多行内容。

"""
    
    # 重复内容以达到约5MB
    full_content = base_content + (repeated_content * 1000)
    content = full_content.encode('utf-8')
    
    # 确保不超过文件大小限制
    if len(content) > 8 * 1024 * 1024:  # 8MB以内
        content = content[:8 * 1024 * 1024]
    
    files = {
        "file": ("large_performance_test.md", BytesIO(content), "text/markdown")
    }
    
    start_time = time.time()
    response = client.post("/convert", files=files)
    end_time = time.time()
    
    conversion_time = end_time - start_time
    
    assert response.status_code == 200
    assert conversion_time < 30.0  # 应该在30秒内完成
    
    print(f"Large file conversion time: {conversion_time:.2f} seconds")
    print(f"File size: {len(content) / 1024 / 1024:.1f} MB")
