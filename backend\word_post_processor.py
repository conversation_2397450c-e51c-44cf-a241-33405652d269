"""
Word文档后处理器 - 修复格式问题
"""

from pathlib import Path
from typing import Dict, Optional
from docx import Document
from docx.shared import Pt
from docx.oxml import OxmlElement
from docx.oxml.ns import qn


class WordPostProcessor:
    """Word文档后处理器 - 修复格式"""
    
    def process_document(self, doc_path: Path, config: Dict) -> bool:
        """处理Word文档，添加目录和修复格式"""
        try:
            print(f"Post-processing Word document: {doc_path}")

            # 打开文档
            doc = Document(str(doc_path))

            # 修复字体设置
            self._fix_font_settings(doc, config)



            # 保存文档
            doc.save(str(doc_path))
            print(f"Document post-processing completed: {doc_path}")
            return True

        except Exception as e:
            print(f"Error in post-processing: {e}")
            return False
    



    

    
    def _fix_font_settings(self, doc: Document, config: Dict):
        """修复字体设置"""
        try:
            chinese_font = config.get('chineseFont', 'Microsoft YaHei')
            english_font = config.get('englishFont', 'Times New Roman')
            font_size = config.get('fontSize', 12)
            
            print(f"Fixing fonts: {chinese_font}, {english_font}, {font_size}pt")
            
            # 修复所有段落的字体
            for paragraph in doc.paragraphs:
                for run in paragraph.runs:
                    if run.text.strip():  # 只处理有文本的run
                        # 设置英文字体
                        run.font.name = english_font
                        run.font.size = Pt(font_size)
                        
                        # 设置中文字体
                        if run._element.rPr is not None:
                            run._element.rPr.rFonts.set(qn('w:eastAsia'), chinese_font)
                        else:
                            # 如果rPr不存在，创建它
                            rPr = OxmlElement('w:rPr')
                            rFonts = OxmlElement('w:rFonts')
                            rFonts.set(qn('w:eastAsia'), chinese_font)
                            rFonts.set(qn('w:ascii'), english_font)
                            rFonts.set(qn('w:hAnsi'), english_font)
                            rPr.append(rFonts)
                            run._element.insert(0, rPr)
            
            # 修复表格中的字体
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            for run in paragraph.runs:
                                if run.text.strip():
                                    run.font.name = english_font
                                    run.font.size = Pt(font_size - 1)  # 表格字体稍小
                                    
                                    if run._element.rPr is not None:
                                        run._element.rPr.rFonts.set(qn('w:eastAsia'), chinese_font)
            
            print("Font settings fixed")
            
        except Exception as e:
            print(f"Error fixing font settings: {e}")
    
    def _apply_heading_styles(self, doc: Document, config: Dict):
        """应用标题样式"""
        try:
            font_size = config.get('fontSize', 12)
            
            # 查找并修复标题
            for paragraph in doc.paragraphs:
                if paragraph.style.name.startswith('Heading'):
                    level = int(paragraph.style.name.split()[-1])
                    
                    # 设置标题字体大小
                    for run in paragraph.runs:
                        run.font.size = Pt(font_size + (7 - level) * 2)
                        run.font.bold = True
                        
        except Exception as e:
            print(f"Error applying heading styles: {e}")



