/* PDF样式文件 - md2word.com */

/* 基础样式 */
body {
    font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
    font-size: 12pt;
    line-height: 1.6;
    margin: 0;
    padding: 2cm;
    color: #333;
    background: white;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
    color: #2c3e50;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    font-weight: bold;
    page-break-after: avoid;
}

h1 { 
    font-size: 24pt; 
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.3em;
}
h2 { 
    font-size: 20pt; 
    border-bottom: 1px solid #bdc3c7;
    padding-bottom: 0.2em;
}
h3 { font-size: 16pt; }
h4 { font-size: 14pt; }
h5 { font-size: 12pt; }
h6 { font-size: 11pt; }

/* 段落样式 */
p {
    margin-bottom: 1em;
    text-align: justify;
    orphans: 2;
    widows: 2;
}

/* 代码样式 */
code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: "Consolas", "Monaco", "Courier New", monospace;
    font-size: 11pt;
    color: #e74c3c;
}

pre {
    background-color: #f8f9fa;
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
    border-left: 4px solid #3498db;
    margin: 1em 0;
    page-break-inside: avoid;
}

pre code {
    background: none;
    padding: 0;
    color: #333;
}

/* 表格样式 */
table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
    page-break-inside: avoid;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
    vertical-align: top;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
    color: #2c3e50;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* 引用样式 */
blockquote {
    border-left: 4px solid #3498db;
    margin: 1em 0;
    padding-left: 1em;
    color: #666;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1em;
    border-radius: 0 5px 5px 0;
}

/* 列表样式 */
ul, ol {
    margin: 1em 0;
    padding-left: 2em;
}

li {
    margin-bottom: 0.5em;
}

/* 链接样式 */
a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* 强调样式 */
strong, b {
    font-weight: bold;
    color: #2c3e50;
}

em, i {
    font-style: italic;
    color: #7f8c8d;
}

/* 分隔线 */
hr {
    border: none;
    border-top: 2px solid #bdc3c7;
    margin: 2em 0;
}

/* 图片样式 */
img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1em auto;
    border-radius: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 页面设置 */
@page {
    margin: 2cm;
    size: A4;
    
    @bottom-center {
        content: "第 " counter(page) " 页";
        font-size: 10pt;
        color: #7f8c8d;
    }
    
    @top-center {
        content: "md2word.com 转换文档";
        font-size: 10pt;
        color: #7f8c8d;
        border-bottom: 1px solid #bdc3c7;
        padding-bottom: 0.5em;
    }
}

/* 首页特殊设置 */
@page :first {
    @top-center {
        content: none;
    }
}

/* 分页控制 */
.page-break {
    page-break-before: always;
}

.no-break {
    page-break-inside: avoid;
}

/* 打印优化 */
@media print {
    body {
        font-size: 11pt;
    }
    
    h1 { font-size: 22pt; }
    h2 { font-size: 18pt; }
    h3 { font-size: 15pt; }
    h4 { font-size: 13pt; }
    h5 { font-size: 11pt; }
    h6 { font-size: 10pt; }
    
    /* 避免在标题后立即分页 */
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    /* 避免在段落中间分页 */
    p, blockquote {
        page-break-inside: avoid;
    }
}

/* 特殊元素样式 */
.highlight {
    background-color: #fff3cd;
    padding: 0.2em 0.4em;
    border-radius: 3px;
}

.note {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 5px;
    padding: 1em;
    margin: 1em 0;
}

.warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
    padding: 1em;
    margin: 1em 0;
}

.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    padding: 1em;
    margin: 1em 0;
}
