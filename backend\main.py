"""
md2word.com - Markdown to Word Converter
FastAPI Backend Application
"""

from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Request

from .config import settings
from .routes import router
from .admin_routes import admin_router
from .utils import check_pandoc_availability
from .database import init_database, test_connection
import platform

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    print(f"{settings.APP_NAME} backend starting...")
    print(f"Version: {settings.APP_VERSION}")

    # 显示系统信息
    system_info = platform.system()
    print(f"🖥️ Operating System: {system_info}")

    # 检查Pandoc是否可用
    if check_pandoc_availability():
        print("✅ Pandoc is available")
    else:
        print("⚠️  Warning: Pandoc not found")

    # 测试数据库连接
    if test_connection():
        print("✅ Database connection successful")
        try:
            init_database()
            print("✅ Database initialized")
        except Exception as e:
            print(f"⚠️  Database initialization failed: {e}")
    else:
        print("⚠️  Warning: Database connection failed")

    # 显示PDF转换策略
    if system_info.lower() == "windows":
        print("📄 PDF Strategy: docx2pdf (Windows) → LibreOffice (fallback)")
    else:
        print("📄 PDF Strategy: LibreOffice (Linux/macOS)")

    print(f"🚀 Server running on http://{settings.HOST}:{settings.PORT}")

    yield

    # 关闭时的清理
    print(f"{settings.APP_NAME} backend shutting down...")

# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    description=settings.APP_DESCRIPTION,
    version=settings.APP_VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=settings.CORS_ALLOW_METHODS,
    allow_headers=settings.CORS_ALLOW_HEADERS,
)

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 包含API路由
app.include_router(router)
app.include_router(admin_router)

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """主页"""
    return templates.TemplateResponse(request, "index.html")



if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "backend.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
