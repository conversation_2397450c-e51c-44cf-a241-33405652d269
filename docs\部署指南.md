# md2word.com 生产环境部署指南

## 🎯 服务器环境说明

**目标域名**：
- 主域名：`md2word.com`
- 备用域名：`www.md2word.com`

**服务器环境**：
- 操作系统：Ubuntu
- 部署目录：`/srv/md2word/`
- 现有服务：Django + MySQL + Nginx（已占用80/443端口）
- 应用端口：`8003`（避免与现有服务冲突）

**注意事项**：
⚠️ **服务器上已有Django网站运行，部署时必须避免端口冲突和配置冲突**

## 🚀 部署准备工作

### 1. 生产环境配置

#### 1.1 环境变量配置
创建 `/srv/md2word/.env.production` 文件：
```bash
# 服务器配置（避免端口冲突）
HOST=127.0.0.1
PORT=8003
DEBUG=False

# 域名配置
ALLOWED_HOSTS=md2word.com,www.md2word.com

# 文件配置
MAX_FILE_SIZE=10485760  # 10MB
FILE_RETENTION_HOURS=2
RATE_LIMIT_PER_DAY=100  # 生产环境放宽限制

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/var/log/md2word/app.log

# 安全配置
SECRET_KEY=md2word-production-secret-key-2024
```

#### 1.2 生产配置文件
修改 `backend/config.py`：
```python
import os
from pathlib import Path

class ProductionSettings(Settings):
    """md2word.com 生产环境配置"""

    # 服务器配置（避免与现有Django网站冲突）
    DEBUG: bool = False
    HOST: str = "127.0.0.1"  # 只监听本地，通过Nginx代理
    PORT: int = 8003         # 使用8003端口避免冲突

    # 文件配置
    UPLOAD_DIR: Path = Path("/srv/md2word/uploads")
    FILE_RETENTION_HOURS: int = int(os.getenv("FILE_RETENTION_HOURS", 2))

    # 限流配置
    RATE_LIMIT_PER_DAY: int = int(os.getenv("RATE_LIMIT_PER_DAY", 100))

    # CORS配置
    CORS_ORIGINS: List[str] = [
        "https://md2word.com",
        "https://www.md2word.com"
    ]
```

### 2. 服务器基础设施

#### 2.1 Nginx配置（与现有Django网站共存）

**重要**：由于服务器已有Django网站，需要在现有Nginx配置中添加新的server块

编辑现有Nginx配置或创建新配置文件：
```bash
# 创建md2word专用配置
sudo nano /etc/nginx/sites-available/md2word.com
```

```nginx
# /etc/nginx/sites-available/md2word.com
# md2word.com 网站配置（与现有Django网站共存）

server {
    listen 80;
    server_name md2word.com www.md2word.com;

    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name md2word.com www.md2word.com;

    # SSL配置（使用Let's Encrypt证书）
    ssl_certificate /etc/letsencrypt/live/md2word.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/md2word.com/privkey.pem;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 文件上传大小限制
    client_max_body_size 10M;

    # 静态文件服务
    location /static/ {
        alias /srv/md2word/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 主应用代理（代理到8003端口）
    location / {
        proxy_pass http://127.0.0.1:8003;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8003/health;
        access_log off;
    }
}
```

**启用配置**：
```bash
# 启用站点配置
sudo ln -s /etc/nginx/sites-available/md2word.com /etc/nginx/sites-enabled/

# 测试Nginx配置
sudo nginx -t

# 重新加载Nginx（不影响现有网站）
sudo systemctl reload nginx
```

#### 2.2 进程管理（Systemd）
```ini
# /etc/systemd/system/md2word.service
[Unit]
Description=md2word.com FastAPI application
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/srv/md2word
Environment=PATH=/srv/md2word/venv/bin
Environment=PYTHONPATH=/srv/md2word
ExecStart=/srv/md2word/venv/bin/python -m uvicorn backend.main:app --host 127.0.0.1 --port 8003 --workers 2
Restart=always
RestartSec=5

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/srv/md2word/uploads /srv/md2word/workspace /var/log/md2word

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
```

**服务管理命令**：
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务（开机自启）
sudo systemctl enable md2word.service

# 启动服务
sudo systemctl start md2word.service

# 查看服务状态
sudo systemctl status md2word.service

# 查看服务日志
sudo journalctl -u md2word.service -f
```

### 3. HTTPS证书配置（Let's Encrypt）

#### 3.1 安装Certbot
```bash
# 安装Certbot和Nginx插件
sudo apt update
sudo apt install certbot python3-certbot-nginx

# 为md2word.com申请SSL证书
sudo certbot --nginx -d md2word.com -d www.md2word.com

# 测试自动续期
sudo certbot renew --dry-run
```

#### 3.2 证书自动续期
```bash
# 添加自动续期任务
sudo crontab -e

# 添加以下行（每天凌晨2点检查续期）
0 2 * * * /usr/bin/certbot renew --quiet --nginx
```

### 4. 安全加固

#### 4.1 防火墙配置（检查现有规则）
```bash
# 检查现有防火墙规则（避免影响现有网站）
sudo ufw status

# 如果需要，确保以下端口开放
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
# 注意：8003端口只允许本地访问，不需要对外开放
```

#### 4.2 文件权限和目录结构
```bash
# 创建项目目录结构
sudo mkdir -p /srv/md2word
sudo mkdir -p /srv/md2word/uploads
sudo mkdir -p /srv/md2word/workspace
sudo mkdir -p /var/log/md2word

# 设置正确的文件权限
sudo chown -R www-data:www-data /srv/md2word
sudo chmod -R 755 /srv/md2word
sudo chmod -R 775 /srv/md2word/uploads    # 上传目录需要写权限
sudo chmod -R 775 /srv/md2word/workspace  # 工作目录需要写权限

# 设置日志目录权限
sudo chown -R www-data:www-data /var/log/md2word
sudo chmod -R 755 /var/log/md2word
```

#### 3.3 日志配置
```python
# backend/logging_config.py
import logging
import logging.handlers
from pathlib import Path

def setup_logging():
    """配置生产环境日志"""
    log_dir = Path("/var/log/md2word")
    log_dir.mkdir(exist_ok=True)
    
    # 配置根日志器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            # 文件日志（轮转）
            logging.handlers.RotatingFileHandler(
                log_dir / "app.log",
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            ),
            # 控制台日志
            logging.StreamHandler()
        ]
    )
```

### 4. 监控和维护

#### 4.1 健康检查
```python
# backend/health.py
from fastapi import APIRouter
import psutil
import os

router = APIRouter()

@router.get("/health/detailed")
async def detailed_health_check():
    """详细健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "system": {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent
        },
        "upload_dir": {
            "exists": settings.UPLOAD_DIR.exists(),
            "files_count": len(list(settings.UPLOAD_DIR.glob("*")))
        },
        "pandoc_available": check_pandoc_availability()
    }
```

#### 4.2 定时清理任务
```bash
# /etc/cron.d/md2word-cleanup
# 每小时清理过期文件
0 * * * * www-data /var/www/md2word/venv/bin/python /var/www/md2word/scripts/cleanup.py

# 每天备份日志
0 2 * * * root /usr/local/bin/backup-logs.sh
```

### 5. 性能优化

#### 5.1 缓存策略
```python
# 添加Redis缓存（可选）
import redis
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expire_time=3600):
    """缓存转换结果"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"conversion:{hash(str(args) + str(kwargs))}"
            
            # 检查缓存
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
            
            # 执行转换
            result = await func(*args, **kwargs)
            
            # 存储缓存
            redis_client.setex(cache_key, expire_time, json.dumps(result))
            return result
        return wrapper
    return decorator
```

#### 5.2 并发处理
```python
# 使用Gunicorn + Uvicorn Workers
# gunicorn_config.py
bind = "127.0.0.1:8001"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 60
keepalive = 2
```

## 📊 文件管理策略建议

### 当前机制分析
✅ **优点**：
- 自动清理，避免磁盘空间耗尽
- UUID命名，避免文件冲突
- 简单可靠，易于维护

⚠️ **需要改进**：
- 文件存储在服务器，增加服务器负担
- 2小时清理可能对某些用户不够友好
- 没有文件访问控制

### 改进建议

#### 方案1：优化现有机制
```python
# 改进的文件管理配置
class ImprovedFileSettings:
    # 分层存储策略
    TEMP_DIR = Path("/tmp/md2word")           # 临时文件
    CACHE_DIR = Path("/var/cache/md2word")    # 缓存文件
    UPLOAD_DIR = Path("/var/md2word/uploads") # 上传文件
    
    # 不同类型文件的保留时间
    TEMP_FILE_RETENTION = 30    # 30分钟
    CACHE_FILE_RETENTION = 24   # 24小时
    UPLOAD_FILE_RETENTION = 2   # 2小时
    
    # 文件大小限制
    MAX_TEMP_DIR_SIZE = 1024 * 1024 * 1024  # 1GB
    MAX_CACHE_DIR_SIZE = 2048 * 1024 * 1024 # 2GB
```

#### 方案2：客户端生成（推荐）
```javascript
// 前端直接生成和下载，不经过服务器存储
async function downloadWordClientSide() {
    const content = getMarkdownContent();
    
    // 发送转换请求，返回二进制流
    const response = await fetch('/convert-stream', {
        method: 'POST',
        body: JSON.stringify({content, config}),
        headers: {'Content-Type': 'application/json'}
    });
    
    // 直接下载，不在服务器存储
    const blob = await response.blob();
    downloadBlob(blob, 'document.docx');
}
```

#### 方案3：云存储集成
```python
# 集成云存储服务
import boto3

class CloudFileManager:
    def __init__(self):
        self.s3 = boto3.client('s3')
        self.bucket = 'md2word-files'
    
    async def store_temp_file(self, file_content, file_id):
        """存储到云端，设置过期时间"""
        self.s3.put_object(
            Bucket=self.bucket,
            Key=f"temp/{file_id}",
            Body=file_content,
            ExpiresIn=7200  # 2小时后自动删除
        )
    
    async def get_download_url(self, file_id):
        """生成临时下载链接"""
        return self.s3.generate_presigned_url(
            'get_object',
            Params={'Bucket': self.bucket, 'Key': f"temp/{file_id}"},
            ExpiresIn=3600  # 1小时有效
        )
```

## 🎯 推荐的部署方案

### 小型部署（个人/小团队）
- **服务器**：1-2核CPU，2-4GB内存
- **存储**：保持现有文件管理机制
- **部署**：Docker + Nginx
- **监控**：基础日志 + 健康检查

### 中型部署（企业内部）
- **服务器**：4核CPU，8GB内存
- **存储**：优化文件管理 + Redis缓存
- **部署**：Kubernetes + 负载均衡
- **监控**：Prometheus + Grafana

### 大型部署（公共服务）
- **服务器**：多节点集群
- **存储**：云存储 + CDN
- **部署**：微服务架构
- **监控**：全链路监控 + 告警

## 📋 完整部署步骤

### 步骤1：服务器准备
```bash
# 1. 连接到服务器
ssh root@your-server-ip

# 2. 更新系统包
sudo apt update && sudo apt upgrade -y

# 3. 安装必要的系统依赖
sudo apt install -y python3 python3-pip python3-venv git nginx certbot python3-certbot-nginx

# 4. 安装Pandoc（必需）
sudo apt install -y pandoc

# 5. 安装Microsoft字体支持（可选，提升PDF质量）
sudo apt install -y ttf-mscorefonts-installer
```

### 步骤2：项目部署
```bash
# 1. 进入部署目录
cd /srv

# 2. 克隆项目代码
sudo git clone https://gitee.com/bin1874/md2word.git md2word

# 3. 设置目录权限
sudo chown -R www-data:www-data /srv/md2word

# 4. 切换到项目目录
cd /srv/md2word

# 5. 创建Python虚拟环境
sudo -u www-data python3 -m venv venv

# 6. 激活虚拟环境并安装依赖
sudo -u www-data bash -c "source venv/bin/activate && pip install -r requirements.txt"

# 7. 创建必要的目录
sudo mkdir -p uploads workspace /var/log/md2word
sudo chown -R www-data:www-data uploads workspace /var/log/md2word
```

### 步骤3：配置文件设置
```bash
# 1. 创建生产环境配置
sudo -u www-data nano /srv/md2word/.env.production

# 2. 复制以下内容到文件中：
HOST=127.0.0.1
PORT=8003
DEBUG=False
ALLOWED_HOSTS=md2word.com,www.md2word.com
MAX_FILE_SIZE=10485760
FILE_RETENTION_HOURS=2
RATE_LIMIT_PER_DAY=100
LOG_LEVEL=INFO
LOG_FILE=/var/log/md2word/app.log
```

### 步骤4：Systemd服务配置
```bash
# 1. 创建systemd服务文件
sudo nano /etc/systemd/system/md2word.service

# 2. 复制服务配置内容（见上文）

# 3. 重新加载systemd并启动服务
sudo systemctl daemon-reload
sudo systemctl enable md2word.service
sudo systemctl start md2word.service

# 4. 检查服务状态
sudo systemctl status md2word.service
```

### 步骤5：Nginx配置
```bash
# 1. 创建Nginx站点配置
sudo nano /etc/nginx/sites-available/md2word.com

# 2. 复制Nginx配置内容（见上文）

# 3. 启用站点
sudo ln -s /etc/nginx/sites-available/md2word.com /etc/nginx/sites-enabled/

# 4. 测试Nginx配置
sudo nginx -t

# 5. 重新加载Nginx
sudo systemctl reload nginx
```

### 步骤6：SSL证书配置
```bash
# 1. 申请Let's Encrypt证书
sudo certbot --nginx -d md2word.com -d www.md2word.com

# 2. 测试自动续期
sudo certbot renew --dry-run

# 3. 设置自动续期
echo "0 2 * * * /usr/bin/certbot renew --quiet --nginx" | sudo crontab -
```

### 步骤7：验证部署
```bash
# 1. 检查服务状态
sudo systemctl status md2word.service

# 2. 检查端口监听
sudo netstat -tlnp | grep 8003

# 3. 检查应用日志
sudo tail -f /var/log/md2word/app.log

# 4. 测试网站访问
curl -I https://md2word.com
```

## 🔧 故障排除

### 常见问题解决

#### 1. 端口冲突
```bash
# 检查端口占用
sudo netstat -tlnp | grep :8003

# 如果端口被占用，修改配置文件中的端口号
sudo nano /srv/md2word/.env.production
```

#### 2. 权限问题
```bash
# 重新设置权限
sudo chown -R www-data:www-data /srv/md2word
sudo chmod -R 775 /srv/md2word/uploads /srv/md2word/workspace
```

#### 3. 依赖问题
```bash
# 重新安装依赖
cd /srv/md2word
sudo -u www-data bash -c "source venv/bin/activate && pip install -r requirements.txt"
```

#### 4. Nginx配置问题
```bash
# 检查Nginx配置语法
sudo nginx -t

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

## 📊 监控和维护

### 日常维护命令
```bash
# 查看服务状态
sudo systemctl status md2word.service

# 重启服务
sudo systemctl restart md2word.service

# 查看实时日志
sudo journalctl -u md2word.service -f

# 检查磁盘使用
df -h /srv/md2word

# 清理过期文件（手动）
find /srv/md2word/uploads -type f -mtime +1 -delete
```

### 性能监控
```bash
# 检查内存使用
ps aux | grep uvicorn

# 检查CPU使用
top -p $(pgrep -f "uvicorn.*md2word")

# 检查网络连接
sudo netstat -an | grep :8003
```

## ⚠️ 重要注意事项

1. **端口冲突**：确保8003端口未被其他服务占用
2. **域名解析**：确保域名已正确解析到服务器IP
3. **防火墙**：确保80和443端口对外开放，8003端口仅本地访问
4. **SSL证书**：Let's Encrypt证书有效期90天，需要自动续期
5. **文件权限**：确保www-data用户有足够的文件操作权限
6. **资源监控**：定期检查磁盘空间和内存使用情况

当前项目已经具备了完整的生产部署能力，按照以上步骤可以安全地部署到与Django网站共存的服务器环境中。
