"""
数据库模型 - md2word.com
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class UserStatistics(Base):
    """用户统计表"""
    __tablename__ = "user_statistics"
    
    id = Column(Integer, primary_key=True, index=True)
    ip_address = Column(String(45), index=True, nullable=False)  # 支持IPv6
    country = Column(String(100), nullable=True)
    country_code = Column(String(2), nullable=True)
    city = Column(String(100), nullable=True)
    user_agent = Column(Text, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    last_access = Column(DateTime, default=func.now(), onupdate=func.now())
    total_conversions = Column(Integer, default=0)
    successful_conversions = Column(Integer, default=0)
    failed_conversions = Column(Integer, default=0)


class ConversionLog(Base):
    """转换记录表"""
    __tablename__ = "conversion_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_ip = Column(String(45), index=True, nullable=False)
    filename = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=False)  # 字节
    output_format = Column(String(10), nullable=False)  # docx, pdf
    content_preview = Column(Text, nullable=True)  # 前500字符
    conversion_time = Column(Float, nullable=True)  # 转换耗时（秒）
    status = Column(String(20), nullable=False)  # success, failed
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False, index=True)


class AdminUser(Base):
    """管理员用户表"""
    __tablename__ = "admin_users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    last_login = Column(DateTime, nullable=True)


class UserFeedback(Base):
    """用户反馈表"""
    __tablename__ = "user_feedback"
    
    id = Column(Integer, primary_key=True, index=True)
    user_ip = Column(String(45), index=True, nullable=False)
    feedback_type = Column(String(20), nullable=False)  # suggestion, bug, feature, other
    content = Column(Text, nullable=False)
    contact_info = Column(String(255), nullable=True)  # 邮箱或其他联系方式
    status = Column(String(20), default="unread", nullable=False)  # unread, read, processed
    created_at = Column(DateTime, default=func.now(), nullable=False, index=True)
    admin_reply = Column(Text, nullable=True)
    replied_at = Column(DateTime, nullable=True)
    replied_by = Column(String(50), nullable=True)  # 管理员用户名
