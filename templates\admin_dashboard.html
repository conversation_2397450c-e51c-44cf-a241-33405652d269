<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - md2word.com</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 24px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: white;
            color: #666;
            transition: all 0.3s;
        }

        .nav-tab.active {
            background: #667eea;
            color: white;
        }

        .nav-tab:hover {
            background: #f0f0f0;
        }

        .nav-tab.active:hover {
            background: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-card .icon {
            font-size: 40px;
            color: #667eea;
            margin-bottom: 15px;
        }

        .stat-card .number {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-card .label {
            color: #666;
            font-size: 14px;
        }

        .data-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-header h3 {
            margin: 0;
            color: #333;
        }

        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .refresh-btn:hover {
            background: #5a6fd8;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 40px;
            color: #c33;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .status-success {
            color: #28a745;
            font-weight: bold;
        }

        .status-failed {
            color: #dc3545;
            font-weight: bold;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
        }

        .pagination button:hover {
            background: #f0f0f0;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-tachometer-alt"></i> md2word.com 管理后台</h1>
        <div class="header-actions">
            <span>欢迎，{{ admin.username }}</span>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> 退出
            </button>
        </div>
    </div>

    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('dashboard')">
                <i class="fas fa-chart-bar"></i> 仪表板
            </button>
            <button class="nav-tab" onclick="showTab('users')">
                <i class="fas fa-users"></i> 用户统计
            </button>
            <button class="nav-tab" onclick="showTab('conversions')">
                <i class="fas fa-file-alt"></i> 转换记录
            </button>
            <button class="nav-tab" onclick="showTab('feedback')">
                <i class="fas fa-comments"></i> 用户反馈
            </button>
        </div>

        <!-- 仪表板 -->
        <div id="dashboard" class="tab-content active">
            <div class="stats-grid" id="statsGrid">
                <div class="loading">加载中...</div>
            </div>
        </div>

        <!-- 用户统计 -->
        <div id="users" class="tab-content">
            <div class="data-table">
                <div class="table-header">
                    <h3>用户统计</h3>
                    <button class="refresh-btn" onclick="loadUsers()">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
                <div id="usersContent">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>

        <!-- 转换记录 -->
        <div id="conversions" class="tab-content">
            <div class="data-table">
                <div class="table-header">
                    <h3>转换记录</h3>
                    <button class="refresh-btn" onclick="loadConversions()">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
                <div id="conversionsContent">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>

        <!-- 用户反馈 -->
        <div id="feedback" class="tab-content">
            <div class="data-table">
                <div class="table-header">
                    <h3>用户反馈</h3>
                    <button class="refresh-btn" onclick="loadFeedback()">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
                <div id="feedbackContent">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/admin.js"></script>
</body>
</html>
