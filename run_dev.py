#!/usr/bin/env python3
"""
开发环境启动脚本 - md2word.com
自动设置SQLite数据库并启动服务
"""

import os
import sys
from pathlib import Path

# 设置开发环境
os.environ["ENVIRONMENT"] = "development"
os.environ["DEBUG"] = "True"

# 检查是否需要初始化数据库
db_file = Path("md2word.db")
need_setup = not db_file.exists()

if need_setup:
    print("🔧 首次运行，正在初始化开发环境...")
    
    # 运行开发环境设置
    try:
        from scripts.setup_dev import main as setup_main
        result = setup_main()
        if result != 0:
            print("❌ 开发环境初始化失败")
            sys.exit(1)
    except ImportError:
        print("❌ 无法导入设置脚本，请先安装依赖: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        sys.exit(1)

# 启动开发服务器
print("\n🚀 启动开发服务器...")
print("数据库: SQLite (md2word.db)")
print("管理后台: http://localhost:8001/admin/login")
print("用户名: admin, 密码: admin123")
print("-" * 50)

if __name__ == "__main__":
    import uvicorn
    from backend.main import app
    
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8001,
        reload=True,
        log_level="info"
    )
