/**
 * md2word.com 编辑器应用
 */

// 全局变量
let currentLanguage = 'en';
let markdownContent = '';

let isPreviewMode = false;
let currentConfig = {
    chineseFont: 'Microsoft YaHei',
    englishFont: 'Times New Roman',
    fontSize: 12
};

// ==================== 目录功能 ====================

// 目录相关变量
let tableOfContents = [];
let currentActiveHeading = null;

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 初始化目录功能
function initializeTableOfContents() {
    // 绑定目录切换checkbox事件
    const tocToggle = document.getElementById('toc-toggle');
    if (tocToggle) {
        tocToggle.addEventListener('change', function() {
            toggleTableOfContents(this.checked);
        });
    }

    // 恢复用户偏好
    const tocVisible = localStorage.getItem('tocVisible');
    if (tocVisible !== null) {
        toggleTableOfContents(tocVisible === 'true');
    }

    // 监听编辑器内容变化
    const editor = document.getElementById('markdown-editor');
    if (editor) {
        editor.addEventListener('input', debounce(updateTableOfContents, 300));
        editor.addEventListener('scroll', debounce(updateActiveHeading, 100));
    }

    // 监听预览区域滚动
    const preview = document.getElementById('preview-content');
    if (preview) {
        preview.addEventListener('scroll', debounce(updateActiveHeading, 100));
    }
}

// 切换目录显示/隐藏
function toggleTableOfContents(show) {
    const tocPanel = document.getElementById('toc-panel');
    const tocToggle = document.getElementById('toc-toggle');

    if (show) {
        tocPanel.classList.remove('hidden');
        tocToggle.checked = true;
    } else {
        tocPanel.classList.add('hidden');
        tocToggle.checked = false;
    }

    // 保存用户偏好
    localStorage.setItem('tocVisible', show);
}

// 解析Markdown内容生成目录
function parseTableOfContents(content) {
    const lines = content.split('\n');
    const toc = [];
    const headingRegex = /^(#{1,6})\s+(.+)$/;

    lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        const match = trimmedLine.match(headingRegex);
        if (match) {
            const level = match[1].length;
            const title = match[2].trim();
            const id = `heading-${index}-${title.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase()}`;

            toc.push({
                level,
                title,
                id,
                lineNumber: index,
                element: null
            });
        }
    });

    return toc;
}

// 更新目录显示
function updateTableOfContents() {
    const editor = document.getElementById('markdown-editor');
    if (!editor) return;

    const content = editor.value;
    tableOfContents = parseTableOfContents(content);

    const tocContent = document.getElementById('toc-content');
    if (!tocContent) return;

    if (tableOfContents.length === 0) {
        tocContent.innerHTML = `
            <div class="toc-empty" data-en="No headings found" data-zh="未找到标题">
                ${currentLanguage === 'zh' ? '未找到标题' : 'No headings found'}
            </div>
        `;
        return;
    }

    // 生成目录HTML
    const tocHTML = tableOfContents.map(item => `
        <div class="toc-item level-${item.level}"
             data-heading-id="${item.id}"
             onclick="scrollToHeading('${item.id}')">
            ${item.title}
        </div>
    `).join('');

    tocContent.innerHTML = tocHTML;

    // 更新预览区域的标题ID
    updatePreviewHeadingIds();
}

// 为预览区域的标题添加ID
function updatePreviewHeadingIds() {
    setTimeout(() => {
        const previewContent = document.getElementById('preview-content');
        if (!previewContent) return;

        const headings = previewContent.querySelectorAll('h1, h2, h3, h4, h5, h6');
        let tocIndex = 0;

        headings.forEach(heading => {
            if (tocIndex < tableOfContents.length) {
                const tocItem = tableOfContents[tocIndex];
                heading.id = tocItem.id;
                tocItem.element = heading;
                tocIndex++;
            }
        });
    }, 100);
}

// 滚动到指定标题
function scrollToHeading(headingId) {
    const previewContent = document.getElementById('preview-content');
    const targetElement = document.getElementById(headingId);

    if (targetElement && previewContent) {
        // 计算滚动位置
        const containerRect = previewContent.getBoundingClientRect();
        const elementRect = targetElement.getBoundingClientRect();
        const scrollTop = previewContent.scrollTop + elementRect.top - containerRect.top - 20;

        // 平滑滚动
        previewContent.scrollTo({
            top: scrollTop,
            behavior: 'smooth'
        });

        // 更新活动标题
        updateActiveHeadingInToc(headingId);
    }
}

// 更新目录中的活动标题
function updateActiveHeadingInToc(headingId) {
    // 移除之前的活动状态
    const prevActive = document.querySelector('.toc-item.active');
    if (prevActive) {
        prevActive.classList.remove('active');
    }

    // 添加新的活动状态
    const newActive = document.querySelector(`[data-heading-id="${headingId}"]`);
    if (newActive) {
        newActive.classList.add('active');
        currentActiveHeading = headingId;
    }
}

// 根据滚动位置更新活动标题
function updateActiveHeading() {
    const previewContent = document.getElementById('preview-content');
    if (!previewContent || tableOfContents.length === 0) return;

    const scrollTop = previewContent.scrollTop;
    const containerHeight = previewContent.clientHeight;

    // 找到当前可见的标题
    let activeHeading = null;
    let minDistance = Infinity;

    tableOfContents.forEach(item => {
        if (item.element) {
            const elementTop = item.element.offsetTop;
            const distance = Math.abs(scrollTop - elementTop + 100); // 100px offset

            if (distance < minDistance && elementTop <= scrollTop + containerHeight / 3) {
                minDistance = distance;
                activeHeading = item.id;
            }
        }
    });

    if (activeHeading && activeHeading !== currentActiveHeading) {
        updateActiveHeadingInToc(activeHeading);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEditor();
    setupEventListeners();
    setupResizers();
    updateLanguage();
    loadConfig();
    initializeTableOfContents();

    // 检测浏览器语言
    const browserLang = navigator.language || navigator.userLanguage;
    if (browserLang.startsWith('zh')) {
        currentLanguage = 'zh';
        updateLanguage();
    }

    // 修复bug：初始化时就更新预览
    setTimeout(() => {
        updatePreview();
        updateTableOfContents();
    }, 100);
});

// 初始化编辑器
function initializeEditor() {
    const editor = document.getElementById('markdown-editor');
    
    // 配置marked选项
    marked.setOptions({
        highlight: function(code, lang) {
            if (lang && hljs.getLanguage(lang)) {
                try {
                    return hljs.highlight(code, { language: lang }).value;
                } catch (err) {}
            }
            return hljs.highlightAuto(code).value;
        },
        breaks: true,
        gfm: true
    });
    
    // 初始内容
    const initialContent = currentLanguage === 'zh' ? 
        `# 欢迎使用 md2word.com 编辑器

这是一个功能强大的 Markdown 编辑器，支持实时预览。

## 主要功能

### 1. 实时编辑
- 在左侧编辑 Markdown 内容
- 右侧实时显示预览效果


### 2. 文件操作
- 支持上传 .md, .markdown, .txt 文件
- 可以粘贴内容直接编辑
- 一键导出为 Word 文档

### 3. 格式支持
- **粗体文本**
- *斜体文本*
- \`行内代码\`
- [链接](https://md2word.com)

#### 代码块示例
\`\`\`python
def hello_world():
    print("Hello, md2word.com!")
    return "转换成功"
\`\`\`

#### 表格示例
| 功能 | 状态 | 说明 |
|------|------|------|
| 实时预览 | ✅ | 支持 |

| Word导出 | ✅ | 支持 |

### 4. 使用说明
1. 在左侧编辑器中输入或粘贴 Markdown 内容
2. 查看右侧实时预览效果
3. 点击"下载 Word"按钮导出文档


开始编辑您的文档吧！` :
        `# Welcome to md2word.com Editor

This is a powerful Markdown editor with live preview and table of contents navigation.

## Main Features

### 1. Live Editing
- Edit Markdown content on the left
- See live preview on the right
- Navigate with table of contents in the middle

### 2. File Operations
- Upload .md, .markdown, .txt files
- Paste content for direct editing
- Export to Word document with one click

### 3. Format Support
- **Bold text**
- *Italic text*
- \`Inline code\`
- [Links](https://md2word.com)

#### Code Block Example
\`\`\`python
def hello_world():
    print("Hello, md2word.com!")
    return "Conversion successful"
\`\`\`

#### Table Example
| Feature | Status | Description |
|---------|--------|-------------|
| Live Preview | ✅ | Supported |

| Word Export | ✅ | Supported |

### 4. How to Use
1. Type or paste Markdown content in the left editor
2. See live preview on the right
3. Click table of contents items to jump to sections
4. Click "Download Word" to export document

Start editing your document!`;
    
    editor.value = initialContent;
    updatePreview();
}

// 设置事件监听器
function setupEventListeners() {
    const editor = document.getElementById('markdown-editor');

    // 编辑器内容变化
    editor.addEventListener('input', function() {
        markdownContent = this.value;
        updatePreview();
    });

    // 文件上传 - 选择文件后自动处理
    document.getElementById('file-input').addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file) {
            console.log('File selected, auto-processing:', file.name);
            // 保存原始文件名
            window.currentFileName = file.name;
            // 自动处理文件并关闭弹窗
            handleFileUpload(event, function() {
                console.log('Auto-processing completed, hiding dialog');
                hideUploadDialog();
            });
        }
    });
    // reference相关功能已移除

    // 拖拽上传
    setupDragAndDrop();


}

// 更新预览
function updatePreview() {
    const previewContent = document.getElementById('preview-content');
    const content = document.getElementById('markdown-editor').value;
    
    if (content.trim() === '') {
        previewContent.innerHTML = `
            <div style="text-align: center; color: #999; padding: 40px 20px;">
                <i class="fas fa-eye" style="font-size: 3em; margin-bottom: 20px; opacity: 0.3;"></i>
                <h3 data-en="Live Preview" data-zh="实时预览">Live Preview</h3>
                <p data-en="Your Markdown content will appear here as you type" data-zh="您输入的 Markdown 内容将在此处实时显示">
                    Your Markdown content will appear here as you type
                </p>
            </div>
        `;
        updateLanguageForElement(previewContent);
        return;
    }
    
    try {
        const html = marked.parse(content);
        previewContent.innerHTML = html;

        // 更新目录（标题ID将由目录功能处理）
        setTimeout(() => {
            updatePreviewHeadingIds();
        }, 50);

    } catch (error) {
        console.error('Markdown parsing error:', error);
        previewContent.innerHTML = `<p style="color: red;">Preview error: ${error.message}</p>`;
    }
}





// 语言切换
function toggleLanguage() {
    currentLanguage = currentLanguage === 'en' ? 'zh' : 'en';
    updateLanguage();
    updatePlaceholder();
}

function updateLanguage() {
    const elements = document.querySelectorAll('[data-en][data-zh]');
    elements.forEach(element => {
        const text = element.getAttribute(`data-${currentLanguage}`);
        if (text) {
            element.textContent = text;
        }
    });
    
    // 更新语言切换按钮
    const langText = document.getElementById('lang-text');
    langText.textContent = currentLanguage === 'en' ? '中文' : 'English';
    
    // 更新HTML lang属性
    document.documentElement.lang = currentLanguage === 'en' ? 'en' : 'zh-CN';
}

function updateLanguageForElement(element) {
    const items = element.querySelectorAll('[data-en][data-zh]');
    items.forEach(item => {
        const text = item.getAttribute(`data-${currentLanguage}`);
        if (text) {
            item.textContent = text;
        }
    });
}

function updatePlaceholder() {
    const editor = document.getElementById('markdown-editor');
    const placeholder = editor.getAttribute(`data-${currentLanguage}-placeholder`);
    if (placeholder) {
        editor.placeholder = placeholder.replace(/&#10;/g, '\n');
    }
}

// 拖拽调整面板大小
function setupResizers() {
    const editorResizer = document.getElementById('editor-resizer');
    const editorPanel = document.getElementById('editor-panel');
    const mainContainer = document.querySelector('.main-container');

    let isResizing = false;

    // 编辑器分隔条
    editorResizer.addEventListener('mousedown', function(e) {
        isResizing = true;
        editorResizer.classList.add('resizing');
        document.body.style.cursor = 'col-resize';
        e.preventDefault();
    });

    document.addEventListener('mousemove', function(e) {
        if (!isResizing) return;

        const containerRect = mainContainer.getBoundingClientRect();
        const mouseX = e.clientX - containerRect.left;
        const containerWidth = containerRect.width;

        const newEditorWidth = (mouseX / containerWidth) * 100;
        if (newEditorWidth >= 25 && newEditorWidth <= 75) {
            editorPanel.style.width = newEditorWidth + '%';
        }
    });

    document.addEventListener('mouseup', function() {
        if (isResizing) {
            isResizing = false;
            document.body.style.cursor = '';
            editorResizer.classList.remove('resizing');
        }
    });
}

// 配置面板功能
function toggleConfigPanel() {
    const panel = document.getElementById('config-panel');
    panel.classList.toggle('open');
}





function updateFontSize(size) {
    currentConfig.fontSize = parseInt(size);
    document.getElementById('font-size-value').textContent = size + 'pt';
    saveConfig();
}

function updateChineseFont(font) {
    currentConfig.chineseFont = font;
    saveConfig();
}

function updateEnglishFont(font) {
    currentConfig.englishFont = font;
    saveConfig();
}

function loadConfig() {
    const saved = localStorage.getItem('md2word-config');
    if (saved) {
        currentConfig = { ...currentConfig, ...JSON.parse(saved) };
    }

    // 应用配置到界面
    document.getElementById('chinese-font').value = currentConfig.chineseFont;
    document.getElementById('english-font').value = currentConfig.englishFont;
    document.getElementById('font-size').value = currentConfig.fontSize;
    document.getElementById('font-size-value').textContent = currentConfig.fontSize + 'pt';
}

function saveConfig() {
    localStorage.setItem('md2word-config', JSON.stringify(currentConfig));
}

// 文件操作函数
function showUploadDialog() {
    document.getElementById('upload-dialog').classList.remove('hidden');
}

function hideUploadDialog() {
    console.log('hideUploadDialog called');
    document.getElementById('upload-dialog').classList.add('hidden');
    // 重置表单
    document.getElementById('file-input').value = '';
    console.log('Upload dialog hidden and form reset');
}

function handleFileUpload(event, callback) {
    console.log('handleFileUpload called with callback:', !!callback);
    const file = event.target.files[0];
    if (!file) {
        console.log('No file found');
        if (callback) callback();
        return;
    }

    console.log('Reading file:', file.name, 'Size:', file.size);
    const reader = new FileReader();
    reader.onload = function(e) {
        console.log('File read successfully');
        const content = e.target.result;
        document.getElementById('markdown-editor').value = content;
        markdownContent = content;
        updatePreview();


        // 文件处理完成后调用回调
        console.log('Calling callback after file processing');
        if (callback) callback();
    };
    reader.onerror = function() {
        console.error('File reading error');
        if (callback) callback();
    };
    reader.readAsText(file);
}

// handleReferenceUpload函数已移除 - 参考模板功能已移到设置面板

// processUploadedFile函数已移除 - 现在文件选择后自动处理

function clearEditor() {
    if (confirm(currentLanguage === 'en' ? 'Clear all content?' : '清空所有内容？')) {
        document.getElementById('markdown-editor').value = '';
        markdownContent = '';
        updatePreview();

    }
}

// 下载Word文档
async function downloadWord(event) {
    const content = document.getElementById('markdown-editor').value.trim();

    if (!content) {
        alert(currentLanguage === 'en' ? 'Please enter some content first' : '请先输入一些内容');
        return;
    }

    // 获取当前文件名（如果有的话）
    let originalFileName = window.currentFileName || 'document';
    // 移除原扩展名并添加.docx
    if (originalFileName.includes('.')) {
        originalFileName = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
    }
    const outputFileName = originalFileName + '.docx';

    // 更可靠的按钮获取方法
    let btn = null;
    if (event && event.target) {
        btn = event.target.closest('button') || event.target;
    } else {
        // 备用方案：直接查找下载按钮
        btn = document.querySelector('.toolbar-btn[onclick*="downloadWord"]') ||
              document.querySelector('button[onclick*="downloadWord"]');
    }

    if (!btn) {
        console.error('Cannot find download button');
        return;
    }

    const originalHTML = btn.innerHTML;
    console.log('Button found:', btn, 'Original HTML:', originalHTML);

    try {
        // 显示加载状态
        btn.innerHTML = '<div class="loading"></div> ' + (currentLanguage === 'en' ? 'Converting...' : '转换中...');
        btn.disabled = true;

        // 创建FormData，直接使用原始内容
        const formData = new FormData();
        const blob = new Blob([content], { type: 'text/markdown' });
        formData.append('file', blob, 'document.md');

        // 添加配置信息
        formData.append('config', JSON.stringify(currentConfig));

        // 参考模板功能已移除，现在使用设置面板中的模板配置

        // 发送请求
        const response = await fetch('/convert', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);

            // 创建下载链接
            const a = document.createElement('a');
            a.href = url;
            a.download = outputFileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // 清理URL
            window.URL.revokeObjectURL(url);

        } else {
            const errorText = await response.text();
            let errorMessage = errorText;

            // 处理常见错误的本地化
            if (response.status === 413) {
                errorMessage = currentLanguage === 'en' ?
                    'Content too large. Maximum size is 10MB.' :
                    '内容过大。最大大小为 10MB。';
            }

            alert(errorMessage);
        }

    } catch (error) {
        console.error('Download error:', error);
        alert(currentLanguage === 'en' ?
            'Network error. Please check your connection and try again.' :
            '网络错误。请检查您的连接并重试。');
    } finally {
        // 恢复按钮状态
        try {
            if (btn && originalHTML) {
                console.log('Restoring button state:', originalHTML);
                btn.innerHTML = originalHTML;
                btn.disabled = false;
                // 确保多语言文本正确显示
                updateLanguageForElement(btn);
                console.log('Button state restored successfully');
            } else {
                console.error('Cannot restore button state - btn or originalHTML is null');
            }
        } catch (restoreError) {
            console.error('Error restoring button state:', restoreError);
            // 强制恢复按钮
            const fallbackBtn = document.querySelector('.toolbar-btn[onclick*="downloadWord"]');
            if (fallbackBtn) {
                fallbackBtn.innerHTML = '<i class="fas fa-download"></i> <span data-en="Download Word" data-zh="下载 Word">Download Word</span>';
                fallbackBtn.disabled = false;
                // 确保多语言文本正确显示
                updateLanguageForElement(fallbackBtn);
            }
        }
    }
}

// 下载PDF文档
async function downloadPDF(event) {
    const content = document.getElementById('markdown-editor').value.trim();

    if (!content) {
        alert(currentLanguage === 'en' ? 'Please enter some content first' : '请先输入一些内容');
        return;
    }

    // 获取当前文件名（如果有的话）
    let originalFileName = window.currentFileName || 'document';
    // 移除原扩展名并添加.pdf
    if (originalFileName.includes('.')) {
        originalFileName = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
    }
    const outputFileName = originalFileName + '.pdf';

    // 更可靠的按钮获取方法
    let btn = null;
    if (event && event.target) {
        btn = event.target.closest('button') || event.target;
    } else {
        // 备用方案：直接查找PDF下载按钮
        btn = document.querySelector('.toolbar-btn[onclick*="downloadPDF"]') ||
              document.querySelector('button[onclick*="downloadPDF"]');
    }

    if (!btn) {
        console.error('Cannot find PDF download button');
        return;
    }

    const originalHTML = btn.innerHTML;
    console.log('PDF Button found:', btn, 'Original HTML:', originalHTML);

    try {
        // 显示加载状态
        btn.innerHTML = '<div class="loading"></div> ' + (currentLanguage === 'en' ? 'Converting...' : '转换中...');
        btn.disabled = true;

        // 创建FormData，直接使用原始内容
        const formData = new FormData();
        const blob = new Blob([content], { type: 'text/markdown' });
        formData.append('file', blob, 'document.md');

        // 添加配置信息
        formData.append('config', JSON.stringify(currentConfig));

        // 指定输出格式为PDF
        formData.append('format', 'pdf');

        // 发送请求
        const response = await fetch('/convert', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);

            // 创建下载链接
            const a = document.createElement('a');
            a.href = url;
            a.download = outputFileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // 清理URL
            window.URL.revokeObjectURL(url);

        } else {
            const errorText = await response.text();
            let errorMessage = errorText;

            // 处理常见错误的本地化
            if (response.status === 413) {
                errorMessage = currentLanguage === 'en' ?
                    'Content too large. Maximum size is 10MB.' :
                    '内容过大。最大大小为 10MB。';
            } else if (response.status === 500 && errorText.includes('weasyprint')) {
                errorMessage = currentLanguage === 'en' ?
                    'PDF conversion is currently unavailable. Please try Word format instead.' :
                    'PDF转换功能暂时不可用。请尝试Word格式。';
            }

            alert(errorMessage);
        }

    } catch (error) {
        console.error('PDF Download error:', error);
        alert(currentLanguage === 'en' ?
            'Network error. Please check your connection and try again.' :
            '网络错误。请检查您的连接并重试。');
    } finally {
        // 恢复按钮状态
        try {
            if (btn && originalHTML) {
                console.log('Restoring PDF button state:', originalHTML);
                btn.innerHTML = originalHTML;
                btn.disabled = false;
                // 确保多语言文本正确显示
                updateLanguageForElement(btn);
                console.log('PDF Button state restored successfully');
            } else {
                console.error('Cannot restore PDF button state - btn or originalHTML is null');
            }
        } catch (restoreError) {
            console.error('Error restoring PDF button state:', restoreError);
            // 强制恢复按钮
            const fallbackBtn = document.querySelector('.toolbar-btn[onclick*="downloadPDF"]');
            if (fallbackBtn) {
                fallbackBtn.innerHTML = '<i class="fas fa-file-pdf"></i> <span data-en="Download PDF" data-zh="下载 PDF">Download PDF</span>';
                fallbackBtn.disabled = false;
                // 确保多语言文本正确显示
                updateLanguageForElement(fallbackBtn);
            }
        }
    }
}

// 上传文件函数已移除，使用编辑器区域的上传按钮



// 拖拽上传功能
function setupDragAndDrop() {
    const uploadZone = document.querySelector('.upload-zone');
    const editor = document.getElementById('markdown-editor');

    // 编辑器拖拽
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        editor.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    editor.addEventListener('drop', handleEditorDrop, false);

    // 上传区域拖拽
    if (uploadZone) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadZone.addEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadZone.addEventListener(eventName, unhighlight, false);
        });

        uploadZone.addEventListener('drop', handleUploadZoneDrop, false);
    }

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight(e) {
        uploadZone.classList.add('dragover');
    }

    function unhighlight(e) {
        uploadZone.classList.remove('dragover');
    }

    function handleEditorDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            const file = files[0];
            if (file.type.includes('text') || file.name.match(/\.(md|markdown|txt)$/i)) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    editor.value = e.target.result;
                    markdownContent = e.target.result;
                    updatePreview();

                };
                reader.readAsText(file);
            }
        }
    }

    function handleUploadZoneDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            document.getElementById('file-input').files = files;
        }
    }
}

// ==================== 反馈功能 ====================

/**
 * 提交用户反馈
 */
async function submitFeedback() {
    const feedbackType = document.getElementById('feedback-type').value;
    const feedbackContent = document.getElementById('feedback-content').value.trim();
    const feedbackContact = document.getElementById('feedback-contact').value.trim();

    // 验证输入
    if (!feedbackContent) {
        alert(currentLanguage === 'zh' ? '请输入反馈内容' : 'Please enter feedback content');
        return;
    }

    if (feedbackContent.length < 10) {
        alert(currentLanguage === 'zh' ? '反馈内容至少需要10个字符' : 'Feedback content must be at least 10 characters');
        return;
    }

    if (feedbackContent.length > 1000) {
        alert(currentLanguage === 'zh' ? '反馈内容不能超过1000个字符' : 'Feedback content must be less than 1000 characters');
        return;
    }

    // 准备表单数据
    const formData = new FormData();
    formData.append('feedback_type', feedbackType);
    formData.append('content', feedbackContent);
    if (feedbackContact) {
        formData.append('contact_info', feedbackContact);
    }

    try {
        // 显示提交中状态
        const submitBtn = event.target;
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ' +
            (currentLanguage === 'zh' ? '提交中...' : 'Submitting...');

        // 发送请求
        const response = await fetch('/feedback', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (response.ok) {
            // 成功提示
            alert(currentLanguage === 'zh' ? '反馈提交成功，感谢您的建议！' : 'Feedback submitted successfully, thank you!');

            // 清空表单
            document.getElementById('feedback-content').value = '';
            document.getElementById('feedback-contact').value = '';
            document.getElementById('feedback-type').selectedIndex = 0;
        } else {
            // 错误提示
            alert(currentLanguage === 'zh' ?
                `提交失败：${result.detail || '未知错误'}` :
                `Submission failed: ${result.detail || 'Unknown error'}`);
        }
    } catch (error) {
        console.error('Feedback submission error:', error);
        alert(currentLanguage === 'zh' ? '网络错误，请稍后重试' : 'Network error, please try again later');
    } finally {
        // 恢复按钮状态
        const submitBtn = event.target;
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> ' +
            (currentLanguage === 'zh' ? '提交反馈' : 'Submit Feedback');
    }
}
