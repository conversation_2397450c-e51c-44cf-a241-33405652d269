#!/usr/bin/env python3
"""
简化的启动脚本 - 不使用reload模式
"""

import uvicorn
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

if __name__ == "__main__":
    print("🚀 Starting md2word.com server (simple mode)...")
    print("📝 Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        uvicorn.run(
            "backend.main:app",
            host="127.0.0.1",
            port=8001,
            reload=False  # 关闭reload模式
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
