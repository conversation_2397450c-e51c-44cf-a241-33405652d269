# Bug分析和修复方案

## 🐛 发现的问题

### Bug 1: 生成的Word中的字体不是web页面设置的字体
**现象**: 用户在前端设置了字体，但生成的Word文档中字体没有生效
**影响**: 用户无法获得期望的字体效果

### Bug 2: Word中的目录应该默认带页码
**现象**: 生成的Word文档目录没有页码显示
**影响**: 目录缺少页码，不符合专业文档标准

### Bug 3: Word中的目录无法点击跳转，提示跳转失败
**现象**: 点击目录项无法跳转到对应章节
**影响**: 目录失去导航功能，用户体验差

## 🔍 根本原因分析

### Bug 1 原因分析
1. **CSS样式限制**: Pandoc转换时CSS样式不能完全映射到Word的原生样式
2. **模板方法错误**: 当前使用HTML+CSS生成模板，但Word需要原生样式定义
3. **字体映射问题**: Word文档的字体设置需要使用Word的样式系统

### Bug 2 原因分析
1. **Pandoc TOC限制**: `--toc`参数生成的目录是简单的超链接列表，不是Word原生目录
2. **缺少页码设置**: 没有使用Word的原生目录功能，无法自动生成页码
3. **模板配置不足**: 需要在Word模板中预设目录样式

### Bug 3 原因分析
1. **超链接格式问题**: Pandoc生成的超链接格式与Word书签不匹配
2. **书签缺失**: Word文档中没有正确的书签标记
3. **目录类型错误**: 使用的是超链接目录而不是Word原生目录

## 🛠️ 修复方案

### 方案1: 使用Word原生样式模板
**思路**: 创建真正的Word模板文件(.dotx)，包含字体和样式定义
**优点**: 字体设置100%生效，符合Word标准
**实现**: 使用python-docx库创建Word模板

### 方案2: 改进Pandoc参数和Lua过滤器
**思路**: 使用Pandoc的高级功能和自定义过滤器
**优点**: 保持当前架构，增强功能
**实现**: 编写Lua过滤器处理字体和目录

### 方案3: 后处理Word文档
**思路**: 生成基础Word文档后，使用python-docx进行后处理
**优点**: 精确控制最终效果
**实现**: 转换后修改字体和目录

## 🎯 推荐解决方案

### 综合方案: 方案1 + 方案3
1. **创建Word原生模板**: 使用python-docx创建包含字体样式的.docx模板
2. **改进Pandoc转换**: 使用更好的参数组合
3. **后处理优化**: 转换后修改目录为Word原生目录

### 实施步骤
1. 安装python-docx库
2. 创建WordTemplateGenerator类
3. 修改转换流程
4. 实现目录后处理
5. 测试验证

## 📋 修复计划

### 阶段1: 修复字体问题 (1-2小时)
- 安装python-docx依赖
- 创建Word原生模板生成器
- 修改转换流程使用Word模板

### 阶段2: 修复目录问题 (1-2小时)  
- 实现Word原生目录生成
- 添加页码和跳转功能
- 优化目录样式

### 阶段3: 测试验证 (30分钟)
- 测试所有字体组合
- 验证目录功能
- 确认跳转正常

## 🔧 技术细节

### Word模板结构
```
Word模板(.docx)
├── 字体样式定义
├── 段落样式定义  
├── 标题样式定义
├── 目录样式定义
└── 页面设置
```

### 目录生成方法
1. 使用Pandoc生成基础文档
2. 使用python-docx插入Word原生目录
3. 设置目录样式和页码格式

### 字体应用方法
1. 在Word模板中定义字体样式
2. 确保样式名称与Pandoc输出匹配
3. 使用python-docx验证字体应用

---

**预期效果**: 修复后用户将获得完美的字体效果和功能完整的目录导航。
