#!/usr/bin/env python3
"""
简化的启动脚本
"""

import uvicorn

if __name__ == "__main__":
    print("🚀 Starting md2word.com server...")
    print("📝 Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        uvicorn.run(
            "backend.main:app",
            host="127.0.0.1",
            port=8001,
            reload=True
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
