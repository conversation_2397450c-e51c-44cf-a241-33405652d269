"""
管理员API路由 - md2word.com
"""

from datetime import timed<PERSON><PERSON>
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Request, Form
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from sqlalchemy.orm import Session

from .config import settings
from .database import get_db
from .auth import authenticate_admin, create_access_token, get_current_admin
from .models import AdminUser, UserStatistics, ConversionLog, UserFeedback
from .services import StatisticsService, FeedbackService

admin_router = APIRouter(prefix="/admin", tags=["admin"])
templates = Jinja2Templates(directory="templates")


# Pydantic模型
class LoginRequest(BaseModel):
    username: str
    password: str


class LoginResponse(BaseModel):
    access_token: str
    token_type: str


class FeedbackResponse(BaseModel):
    id: int
    user_ip: str
    feedback_type: str
    content: str
    contact_info: Optional[str]
    status: str
    created_at: str
    admin_reply: Optional[str]
    replied_at: Optional[str]


# 管理员登录页面
@admin_router.get("/login", response_class=HTMLResponse)
async def admin_login_page(request: Request):
    """管理员登录页面"""
    return templates.TemplateResponse(request, "admin_login.html")


# 管理员登录API
@admin_router.post("/login", response_model=LoginResponse)
async def admin_login(login_data: LoginRequest):
    """管理员登录"""
    admin = authenticate_admin(login_data.username, login_data.password)
    if not admin:
        raise HTTPException(
            status_code=401,
            detail="Invalid username or password"
        )
    
    access_token_expires = timedelta(minutes=settings.JWT_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": admin.username},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }


# 管理员仪表板页面
@admin_router.get("/dashboard", response_class=HTMLResponse)
async def admin_dashboard(
    request: Request,
    current_admin: AdminUser = Depends(get_current_admin)
):
    """管理员仪表板页面"""
    return templates.TemplateResponse(
        request, 
        "admin_dashboard.html",
        {"admin": current_admin}
    )


# 获取仪表板统计数据
@admin_router.get("/api/dashboard/stats")
async def get_dashboard_stats(
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取仪表板统计数据"""
    stats = StatisticsService.get_dashboard_stats()
    return stats


# 获取用户统计列表
@admin_router.get("/api/users")
async def get_user_statistics(
    page: int = 1,
    limit: int = 50,
    current_admin: AdminUser = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取用户统计列表"""
    offset = (page - 1) * limit
    
    users = db.query(UserStatistics).order_by(
        UserStatistics.last_access.desc()
    ).offset(offset).limit(limit).all()
    
    total = db.query(UserStatistics).count()
    
    return {
        "users": users,
        "total": total,
        "page": page,
        "limit": limit,
        "pages": (total + limit - 1) // limit
    }


# 获取转换记录列表
@admin_router.get("/api/conversions")
async def get_conversion_logs(
    page: int = 1,
    limit: int = 50,
    status: Optional[str] = None,
    format: Optional[str] = None,
    current_admin: AdminUser = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取转换记录列表"""
    offset = (page - 1) * limit
    
    query = db.query(ConversionLog)
    
    if status:
        query = query.filter(ConversionLog.status == status)
    if format:
        query = query.filter(ConversionLog.output_format == format)
    
    conversions = query.order_by(
        ConversionLog.created_at.desc()
    ).offset(offset).limit(limit).all()
    
    total = query.count()
    
    return {
        "conversions": conversions,
        "total": total,
        "page": page,
        "limit": limit,
        "pages": (total + limit - 1) // limit
    }


# 获取反馈列表
@admin_router.get("/api/feedback")
async def get_feedback_list(
    page: int = 1,
    limit: int = 50,
    status: Optional[str] = None,
    current_admin: AdminUser = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取用户反馈列表"""
    offset = (page - 1) * limit
    
    query = db.query(UserFeedback)
    if status:
        query = query.filter(UserFeedback.status == status)
    
    feedbacks = query.order_by(
        UserFeedback.created_at.desc()
    ).offset(offset).limit(limit).all()
    
    total = query.count()
    
    return {
        "feedbacks": feedbacks,
        "total": total,
        "page": page,
        "limit": limit,
        "pages": (total + limit - 1) // limit
    }


# 更新反馈状态
@admin_router.put("/api/feedback/{feedback_id}")
async def update_feedback(
    feedback_id: int,
    status: str = Form(...),
    admin_reply: Optional[str] = Form(None),
    current_admin: AdminUser = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """更新反馈状态和回复"""
    feedback = db.query(UserFeedback).filter(UserFeedback.id == feedback_id).first()
    if not feedback:
        raise HTTPException(status_code=404, detail="Feedback not found")
    
    feedback.status = status
    if admin_reply:
        feedback.admin_reply = admin_reply
        feedback.replied_by = current_admin.username
        from datetime import datetime
        feedback.replied_at = datetime.utcnow()
    
    db.commit()
    
    return {"message": "Feedback updated successfully"}


# 用户管理页面
@admin_router.get("/users", response_class=HTMLResponse)
async def admin_users_page(
    request: Request,
    current_admin: AdminUser = Depends(get_current_admin)
):
    """用户管理页面"""
    return templates.TemplateResponse(
        request,
        "admin_users.html",
        {"admin": current_admin}
    )


# 转换记录页面
@admin_router.get("/conversions", response_class=HTMLResponse)
async def admin_conversions_page(
    request: Request,
    current_admin: AdminUser = Depends(get_current_admin)
):
    """转换记录页面"""
    return templates.TemplateResponse(
        request,
        "admin_conversions.html",
        {"admin": current_admin}
    )


# 反馈管理页面
@admin_router.get("/feedback", response_class=HTMLResponse)
async def admin_feedback_page(
    request: Request,
    current_admin: AdminUser = Depends(get_current_admin)
):
    """反馈管理页面"""
    return templates.TemplateResponse(
        request,
        "admin_feedback.html",
        {"admin": current_admin}
    )
