"""
业务服务层 - md2word.com
"""

import time
import asyncio
import requests
from typing import Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func

from .config import settings
from .database import get_db_context
from .models import UserStatistics, ConversionLog, UserFeedback


class GeolocationService:
    """地理位置服务"""
    
    @staticmethod
    async def get_location_info(ip_address: str) -> Dict[str, Any]:
        """获取IP地址的地理位置信息"""
        try:
            # 跳过本地IP
            if ip_address in ["127.0.0.1", "localhost", "::1"]:
                return {
                    "country": "Local",
                    "country_code": "LC",
                    "city": "Local"
                }
            
            # 异步请求地理位置API
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.get(
                    f"{settings.GEOLOCATION_API_URL}{ip_address}",
                    timeout=5
                )
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    return {
                        "country": data.get("country", "Unknown"),
                        "country_code": data.get("countryCode", "UN"),
                        "city": data.get("city", "Unknown")
                    }
        except Exception as e:
            print(f"获取地理位置信息失败: {e}")
        
        return {
            "country": "Unknown",
            "country_code": "UN",
            "city": "Unknown"
        }


class StatisticsService:
    """统计服务"""
    
    @staticmethod
    async def record_user_access(ip_address: str, user_agent: str = None):
        """记录用户访问"""
        try:
            with get_db_context() as db:
                # 查找或创建用户统计记录
                user_stat = db.query(UserStatistics).filter(
                    UserStatistics.ip_address == ip_address
                ).first()
                
                if not user_stat:
                    # 获取地理位置信息
                    location_info = await GeolocationService.get_location_info(ip_address)
                    
                    # 创建新用户统计记录
                    user_stat = UserStatistics(
                        ip_address=ip_address,
                        country=location_info["country"],
                        country_code=location_info["country_code"],
                        city=location_info["city"],
                        user_agent=user_agent
                    )
                    db.add(user_stat)
                else:
                    # 更新最后访问时间
                    user_stat.last_access = datetime.utcnow()
                
                db.commit()
        except Exception as e:
            print(f"记录用户访问失败: {e}")
    
    @staticmethod
    async def record_conversion(
        ip_address: str,
        filename: str,
        file_size: int,
        output_format: str,
        content_preview: str,
        conversion_time: float,
        status: str,
        error_message: str = None
    ):
        """记录转换操作"""
        try:
            with get_db_context() as db:
                # 创建转换记录
                conversion_log = ConversionLog(
                    user_ip=ip_address,
                    filename=filename,
                    file_size=file_size,
                    output_format=output_format,
                    content_preview=content_preview[:500] if content_preview else None,
                    conversion_time=conversion_time,
                    status=status,
                    error_message=error_message
                )
                db.add(conversion_log)
                
                # 更新用户统计
                user_stat = db.query(UserStatistics).filter(
                    UserStatistics.ip_address == ip_address
                ).first()
                
                if user_stat:
                    user_stat.total_conversions += 1
                    if status == "success":
                        user_stat.successful_conversions += 1
                    else:
                        user_stat.failed_conversions += 1
                    user_stat.last_access = datetime.utcnow()
                
                db.commit()
        except Exception as e:
            print(f"记录转换操作失败: {e}")
    
    @staticmethod
    def get_dashboard_stats() -> Dict[str, Any]:
        """获取仪表板统计数据"""
        try:
            with get_db_context() as db:
                # 今日统计
                today = datetime.now().date()
                today_conversions = db.query(ConversionLog).filter(
                    func.date(ConversionLog.created_at) == today
                ).count()
                
                today_success = db.query(ConversionLog).filter(
                    func.date(ConversionLog.created_at) == today,
                    ConversionLog.status == "success"
                ).count()
                
                # 总统计
                total_conversions = db.query(ConversionLog).count()
                total_users = db.query(UserStatistics).count()
                
                # 成功率
                success_rate = 0
                if total_conversions > 0:
                    total_success = db.query(ConversionLog).filter(
                        ConversionLog.status == "success"
                    ).count()
                    success_rate = (total_success / total_conversions) * 100
                
                # 热门格式
                format_stats = db.query(
                    ConversionLog.output_format,
                    func.count(ConversionLog.id).label('count')
                ).group_by(ConversionLog.output_format).all()
                
                return {
                    "today_conversions": today_conversions,
                    "today_success": today_success,
                    "total_conversions": total_conversions,
                    "total_users": total_users,
                    "success_rate": round(success_rate, 2),
                    "format_stats": [{"format": f[0], "count": f[1]} for f in format_stats]
                }
        except Exception as e:
            print(f"获取仪表板统计失败: {e}")
            return {}


class FeedbackService:
    """反馈服务"""
    
    @staticmethod
    async def submit_feedback(
        ip_address: str,
        feedback_type: str,
        content: str,
        contact_info: str = None
    ) -> bool:
        """提交用户反馈"""
        try:
            with get_db_context() as db:
                feedback = UserFeedback(
                    user_ip=ip_address,
                    feedback_type=feedback_type,
                    content=content,
                    contact_info=contact_info
                )
                db.add(feedback)
                db.commit()
                return True
        except Exception as e:
            print(f"提交反馈失败: {e}")
            return False
    
    @staticmethod
    def get_feedback_list(status: str = None, limit: int = 50, offset: int = 0):
        """获取反馈列表"""
        try:
            with get_db_context() as db:
                query = db.query(UserFeedback)
                if status:
                    query = query.filter(UserFeedback.status == status)
                
                feedbacks = query.order_by(
                    UserFeedback.created_at.desc()
                ).offset(offset).limit(limit).all()
                
                return feedbacks
        except Exception as e:
            print(f"获取反馈列表失败: {e}")
            return []
