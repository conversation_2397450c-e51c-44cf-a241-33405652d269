#!/bin/bash

# md2word.com 生产环境问题修复脚本
# 解决 "转换过程中出现错误: 500: Conversion failed" 问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
    log_error "此脚本需要root权限运行"
    exit 1
fi

echo "=========================================="
echo "    md2word.com 生产环境问题修复"
echo "=========================================="

# 1. 停止服务
log_info "停止md2word服务..."
systemctl stop md2word.service

# 2. 修复文件权限
log_info "修复文件权限..."
chown -R www-data:www-data /srv/md2word
chmod -R 755 /srv/md2word
chmod -R 775 /srv/md2word/uploads /srv/md2word/workspace

# 创建并设置日志目录权限
mkdir -p /var/log/md2word
chown -R www-data:www-data /var/log/md2word
chmod -R 755 /var/log/md2word

# 确保临时目录权限
chmod 1777 /tmp

log_success "文件权限修复完成"

# 3. 检查并安装依赖
log_info "检查系统依赖..."

# 检查Pandoc
if ! command -v pandoc &> /dev/null; then
    log_warning "Pandoc未安装，正在安装..."
    apt update
    apt install -y pandoc
else
    log_success "Pandoc已安装: $(pandoc --version | head -1)"
fi

# 检查LibreOffice
if ! command -v libreoffice &> /dev/null; then
    log_warning "LibreOffice未安装，正在安装..."
    apt install -y libreoffice
else
    log_success "LibreOffice已安装"
fi

# 安装字体支持
log_info "安装字体支持..."
apt install -y fonts-liberation fonts-dejavu-core fonts-noto-cjk

# 4. 重新安装Python依赖
log_info "重新安装Python依赖..."
cd /srv/md2word

sudo -u www-data bash -c "
source venv/bin/activate
pip install --upgrade pip
pip install --upgrade --force-reinstall docx2pdf python-docx
"

# 5. 测试转换功能
log_info "测试转换功能..."

# 创建测试脚本
cat > /tmp/test_conversion.py << 'EOF'
import os
import sys
import tempfile
import subprocess
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '/srv/md2word')

def test_pandoc():
    """测试Pandoc转换"""
    print("测试Pandoc转换...")
    
    test_md = """# 测试文档
这是一个测试文档。
## 功能测试
- 列表项1
- 列表项2
**粗体文本** 和 *斜体文本*
"""
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            md_file = Path(temp_dir) / "test.md"
            docx_file = Path(temp_dir) / "test.docx"
            
            # 写入测试文件
            md_file.write_text(test_md, encoding='utf-8')
            
            # 测试Pandoc转换
            cmd = [
                "pandoc",
                str(md_file),
                "-o", str(docx_file),
                "--from", "markdown",
                "--to", "docx"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0 and docx_file.exists():
                print("✅ Pandoc转换成功")
                return True
            else:
                print(f"❌ Pandoc转换失败: {result.stderr}")
                return False
                
    except Exception as e:
        print(f"❌ Pandoc测试异常: {e}")
        return False

def test_pdf_conversion():
    """测试PDF转换"""
    print("测试PDF转换...")
    
    try:
        from docx2pdf import convert
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建一个简单的docx文件进行测试
            docx_file = Path(temp_dir) / "test.docx"
            pdf_file = Path(temp_dir) / "test.pdf"
            
            # 使用pandoc创建docx文件
            test_md = "# 测试PDF转换\n\n这是一个测试文档。"
            md_file = Path(temp_dir) / "test.md"
            md_file.write_text(test_md, encoding='utf-8')
            
            subprocess.run([
                "pandoc", str(md_file), "-o", str(docx_file)
            ], check=True)
            
            # 转换为PDF
            convert(str(docx_file), str(pdf_file))
            
            if pdf_file.exists() and pdf_file.stat().st_size > 0:
                print("✅ PDF转换成功")
                return True
            else:
                print("❌ PDF转换失败：文件未生成或为空")
                return False
                
    except Exception as e:
        print(f"❌ PDF转换异常: {e}")
        return False

if __name__ == "__main__":
    print("开始转换功能测试...")
    
    pandoc_ok = test_pandoc()
    pdf_ok = test_pdf_conversion()
    
    if pandoc_ok and pdf_ok:
        print("✅ 所有转换测试通过")
        sys.exit(0)
    else:
        print("❌ 转换测试失败")
        sys.exit(1)
EOF

# 以www-data用户运行测试
if sudo -u www-data bash -c "cd /srv/md2word && source venv/bin/activate && python /tmp/test_conversion.py"; then
    log_success "转换功能测试通过"
else
    log_error "转换功能测试失败"
fi

# 清理测试文件
rm -f /tmp/test_conversion.py

# 6. 更新systemd配置（确保环境变量正确）
log_info "更新systemd配置..."

cat > /etc/systemd/system/md2word.service << 'EOF'
[Unit]
Description=md2word.com FastAPI application
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/srv/md2word
Environment=PATH=/srv/md2word/venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
Environment=PYTHONPATH=/srv/md2word
Environment=HOME=/var/lib/www-data
ExecStart=/srv/md2word/venv/bin/python -m uvicorn backend.main:app --host 127.0.0.1 --port 8003 --workers 2
Restart=always
RestartSec=5

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/srv/md2word/uploads /srv/md2word/workspace /var/log/md2word /tmp

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload

# 7. 启动服务
log_info "启动md2word服务..."
systemctl start md2word.service

# 等待服务启动
sleep 5

# 8. 验证服务状态
if systemctl is-active --quiet md2word.service; then
    log_success "md2word服务启动成功"
    
    # 检查端口监听
    if netstat -tlnp | grep ":8003 " > /dev/null; then
        log_success "端口8003监听正常"
        
        # 测试健康检查
        if curl -s http://127.0.0.1:8003/health > /dev/null; then
            log_success "健康检查通过"
        else
            log_warning "健康检查失败"
        fi
    else
        log_error "端口8003未监听"
    fi
else
    log_error "md2word服务启动失败"
    systemctl status md2word.service
fi

# 9. 显示诊断信息
echo ""
echo "=========================================="
echo "           修复完成"
echo "=========================================="
echo ""
echo "服务状态检查："
echo "  systemctl status md2word.service"
echo ""
echo "查看实时日志："
echo "  journalctl -u md2word.service -f"
echo ""
echo "测试网站访问："
echo "  curl -I http://127.0.0.1:8003"
echo ""
echo "如果问题仍然存在，请查看详细日志："
echo "  sudo tail -f /var/log/md2word/app.log"
echo ""
echo "=========================================="
