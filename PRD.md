# 产品需求文档（PRD）

## 1. 产品概述

**产品名称：** md2word.com
**目标：** 提供一款快速、稳定、基于浏览器的在线服务，将 Markdown 文件转换为 Microsoft Word（.docx），同时完整保留标题层级、代码高亮、图片、表格、Front‑matter 元数据等格式。
**目标用户：** 习惯用 Markdown 写作却需向甲方或同事提交 Word 文档的开发者、技术写作者、学生与内容创作者。

## 2. 问题陈述

Markdown 适合撰写草稿，但项目干系人通常要求 Word 格式。现有免费转换器缺少批量功能、模板控制、中文兼容度不佳，用户不得不手动二次排版。

## 3. 目标与成功指标

| 目标         | 指标 (KPI)               | 90 天目标 |
| ---------- | ---------------------- | ------ |
| 转换速度       | P95 延迟 ≤ 2 秒（文件 <2 MB） | ≥ 95%  |
| 可靠性        | 转换成功率                  | ≥ 99%  |
| 用户增长       | 月活用户（MAU）              | 5,000  |
| 收入（Pro 套餐） | 付费订阅用户                 | 200    |

## 4. 关键功能（MVP）

1. **单文件上传 / 拖拽**（支持 `.md`、`.markdown`、`.txt`）
2. **.docx 下载**，实现：

   * Markdown 标题 → Word 样式（H1→标题 1 …）
   * 代码块语法高亮
   * 表格、引用块、列表、脚注
   * 本地 / 远程图片自动嵌入
3. **参考模板上传**（`reference.docx`），继承字体、页边距、页眉页脚。
4. **双语界面：** 英文 + 简体中文
5. **限流：** 未登录用户每天 ≤ 5 次转换
6. **隐私：** 文件 2 小时后自动删除

### 延伸功能（Phase 2+）

* 批量上传  批量下载 

* AI「智能美化」（应用企业模板）
* 账户体系 & Stripe/支付宝 支付

## 5. 用户故事（MVP）

| ID | 作为……  | 我想要……        | 以便……             |
| -- | ----- | ------------ | ---------------- |
| U1 | 开发者   | 上传 README.md | 可以把 .docx 发给产品经理 |
| U2 | 技术写作者 | 保留原始代码配色     | 文档符合品牌规范         |
| U3 | 学生    | 使用中文界面       | 不用挣扎英文菜单         |
| U4 | 市场经理  | 套用公司模板       | 文档保持品牌一致         |

## 6. 非功能性需求

* **性能：** 服务器冷启动 ≤ 50 ms；队列可横向扩展
* **安全：** 全站 TLS 1.3；转换容器沙箱化（无外网）
* **合规：** GDPR & 中国网信办合规文件清理
* **可观测性：** Grafana 仪表板监控延迟、错误率、队列深度

## 7. 技术栈

* **前端：** Next.js / Vue 3 + TailwindCSS
* **后端：** FastAPI（Python 3.12），封装 Pandoc 3.2
* **队列：** Redis + Celery
* **存储：** 兼容 S3（MinIO / AWS S3）
* **容器运行时：** Docker（seccomp，内存 512 MB 限制）

## 8. 不包含（MVP）

* PPT / PDF 导出
* 实时协同编辑器
* 移动原生 App

## 9. 依赖与风险

* Pandoc 对部分扩展（数学公式、图表）支持不足
* 高峰时大文件上传排队（通过队列 + 10 MB 尺寸限制缓解）
* Stripe / 支付宝 接入审核周期

## 10. 里程碑

| 里程碑          | 日期   | 负责人      |
| ------------ | ---- | -------- |
| 需求文档确认       | T+0  | PM       |
| MVP 开发完成     | T+21 | Eng Lead |
| Beta 内测（邀请制） | T+30 | Growth   |
| 公测上线         | T+45 | PM       |
| 付费计划上线       | T+60 | Biz Dev  |

---

**作者：** 产品经理  —  2025‑07‑14

新增需求：
1 bug： 需要手动输入内容才能显示出目录
2 目前的3列尺寸 Markdown Editor  ，Table of Contents ， Live Preview 我希望都可以拖动左右的大小空间
3 web页面中目录的字体默认小一些，否则显示很乱。 另外目录的字体大小可以拖动缩小。
4 用户可以为word文档设置常用的中英文字体 ， 目前的默认字体太难看了。
5 加入word文档目录生成的功能，导出的word可以带目录。用户可以设置模版的显示级别，以及是否需要生成目录。
6 系统内置几种不同风格的word模版功能，用户可以选择不同的模版。 例如浅色模版 深色模版 商务风格模版。
7 为以上配置功能设计合理的配置面板，基于上述所有功能，重新设计整个web页面。 使得web页面简单 易用 符合审美 直观  美观。


=================

使用以下域名
www.md2word.com
md2word.com
服务器上面已经部署了一个网站，所以这个网站的相关配置例如端口号不能和之前的网站冲突。
之前的网站基于django mysql nginx

这个网站放在这个目录：
root@VM-0-12-ubuntu:/srv# 

另外需要配置 HTTPS (使用 Certbot 和 Let's Encrypt)

